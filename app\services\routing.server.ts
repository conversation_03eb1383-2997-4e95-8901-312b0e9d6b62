/**
 * Routing Service
 * Standardizes routing, navigation, and URL management across the application
 */

import { redirect } from "@remix-run/node";
import { createError, logError, type ErrorContext } from "../utils/error-handling.server";

export interface RouteConfig {
  path: string;
  name: string;
  title: string;
  description?: string;
  requiresAuth: boolean;
  requiresBilling?: boolean;
  allowedMethods: string[];
  rateLimit?: {
    windowMs: number;
    maxRequests: number;
  };
}

export interface NavigationItem {
  name: string;
  path: string;
  icon?: string;
  badge?: string | number;
  children?: NavigationItem[];
  requiresBilling?: boolean;
  isActive?: boolean;
}

export interface BreadcrumbItem {
  name: string;
  path?: string;
  isActive?: boolean;
}

/**
 * Centralized routing and navigation service
 */
export class RoutingService {
  private static instance: RoutingService;
  private routes: Map<string, RouteConfig> = new Map();

  static getInstance(): RoutingService {
    if (!RoutingService.instance) {
      RoutingService.instance = new RoutingService();
      RoutingService.instance.initializeRoutes();
    }
    return RoutingService.instance;
  }

  /**
   * Initialize application routes
   */
  private initializeRoutes(): void {
    const routes: RouteConfig[] = [
      {
        path: '/app',
        name: 'dashboard',
        title: 'Dashboard',
        description: 'Main application dashboard',
        requiresAuth: true,
        allowedMethods: ['GET']
      },
      {
        path: '/app/seo-dashboard',
        name: 'seo-dashboard',
        title: 'SEO Dashboard',
        description: 'SEO optimization dashboard',
        requiresAuth: true,
        requiresBilling: true,
        allowedMethods: ['GET', 'POST']
      },
      {
        path: '/app/billing',
        name: 'billing',
        title: 'Billing',
        description: 'Billing and subscription management',
        requiresAuth: true,
        allowedMethods: ['GET', 'POST']
      },
      {
        path: '/app/settings',
        name: 'settings',
        title: 'Settings',
        description: 'Application settings',
        requiresAuth: true,
        allowedMethods: ['GET', 'POST']
      },
      {
        path: '/app/help',
        name: 'help',
        title: 'Help & Support',
        description: 'Help documentation and support',
        requiresAuth: true,
        allowedMethods: ['GET']
      }
    ];

    routes.forEach(route => {
      this.routes.set(route.name, route);
    });

    console.log(`📍 Initialized ${routes.length} application routes`);
  }

  /**
   * Get route configuration by name
   */
  getRoute(name: string): RouteConfig | undefined {
    return this.routes.get(name);
  }

  /**
   * Get all routes
   */
  getAllRoutes(): RouteConfig[] {
    return Array.from(this.routes.values());
  }

  /**
   * Build URL with query parameters
   */
  buildUrl(routeName: string, params?: Record<string, string | number>): string {
    const route = this.getRoute(routeName);
    if (!route) {
      console.warn(`⚠️ Route not found: ${routeName}`);
      return '/app';
    }

    let url = route.path;

    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        searchParams.append(key, value.toString());
      });
      
      if (searchParams.toString()) {
        url += `?${searchParams.toString()}`;
      }
    }

    return url;
  }

  /**
   * Create safe redirect response
   */
  createRedirect(
    routeName: string,
    params?: Record<string, string | number>,
    options?: { status?: number; headers?: Record<string, string> }
  ): Response {
    const url = this.buildUrl(routeName, params);
    const status = options?.status || 302;
    const headers = options?.headers || {};

    console.log(`🔄 Redirecting to: ${url} (${status})`);
    return redirect(url, { status, headers });
  }

  /**
   * Validate route access
   */
  validateRouteAccess(
    routeName: string,
    context: {
      isAuthenticated: boolean;
      hasBilling: boolean;
      method: string;
    }
  ): { allowed: boolean; reason?: string } {
    const route = this.getRoute(routeName);
    if (!route) {
      return { allowed: false, reason: 'Route not found' };
    }

    // Check authentication
    if (route.requiresAuth && !context.isAuthenticated) {
      return { allowed: false, reason: 'Authentication required' };
    }

    // Check billing
    if (route.requiresBilling && !context.hasBilling) {
      return { allowed: false, reason: 'Active billing required' };
    }

    // Check HTTP method
    if (!route.allowedMethods.includes(context.method)) {
      return { allowed: false, reason: 'Method not allowed' };
    }

    return { allowed: true };
  }

  /**
   * Get navigation items for current user
   */
  getNavigationItems(context: {
    isAuthenticated: boolean;
    hasBilling: boolean;
    currentPath: string;
  }): NavigationItem[] {
    const items: NavigationItem[] = [
      {
        name: 'Dashboard',
        path: '/app',
        icon: 'dashboard'
      },
      {
        name: 'SEO Optimization',
        path: '/app/seo-dashboard',
        icon: 'search',
        requiresBilling: true
      },
      {
        name: 'Billing',
        path: '/app/billing',
        icon: 'credit-card'
      },
      {
        name: 'Settings',
        path: '/app/settings',
        icon: 'settings'
      },
      {
        name: 'Help',
        path: '/app/help',
        icon: 'help'
      }
    ];

    // Filter items based on user context
    const filteredItems = items.filter(item => {
      if (item.requiresBilling && !context.hasBilling) {
        return false;
      }
      return true;
    });

    // Mark active item
    return filteredItems.map(item => ({
      ...item,
      isActive: context.currentPath === item.path || 
                context.currentPath.startsWith(item.path + '/')
    }));
  }

  /**
   * Generate breadcrumbs for current path
   */
  generateBreadcrumbs(path: string): BreadcrumbItem[] {
    const breadcrumbs: BreadcrumbItem[] = [
      { name: 'Home', path: '/app' }
    ];

    // Parse path segments
    const segments = path.split('/').filter(Boolean);
    
    if (segments.length > 1) { // Skip 'app' segment
      let currentPath = '';
      
      for (let i = 1; i < segments.length; i++) {
        const segment = segments[i];
        currentPath += `/${segment}`;
        
        // Find route for this path
        const route = Array.from(this.routes.values())
          .find(r => r.path === `/app${currentPath}`);
        
        if (route) {
          breadcrumbs.push({
            name: route.title,
            path: route.path,
            isActive: i === segments.length - 1
          });
        } else {
          // Fallback to segment name
          breadcrumbs.push({
            name: this.formatSegmentName(segment),
            path: `/app${currentPath}`,
            isActive: i === segments.length - 1
          });
        }
      }
    }

    return breadcrumbs;
  }

  /**
   * Handle route errors with appropriate redirects
   */
  handleRouteError(
    error: Error,
    context: {
      path: string;
      isAuthenticated: boolean;
      hasBilling: boolean;
    }
  ): Response {
    console.error(`❌ Route error on ${context.path}:`, error);

    // Authentication errors
    if (error.message.includes('authentication') || error.message.includes('unauthorized')) {
      return this.createRedirect('dashboard');
    }

    // Billing errors
    if (error.message.includes('billing') || error.message.includes('subscription')) {
      return this.createRedirect('billing');
    }

    // Rate limiting errors
    if (error.message.includes('rate limit')) {
      return new Response('Rate limit exceeded. Please try again later.', {
        status: 429,
        headers: { 'Retry-After': '60' }
      });
    }

    // Default error handling
    return this.createRedirect('dashboard');
  }

  /**
   * Get route metadata for SEO
   */
  getRouteMetadata(routeName: string): {
    title: string;
    description: string;
    canonical?: string;
  } {
    const route = this.getRoute(routeName);
    if (!route) {
      return {
        title: 'ProdRankX - SEO Optimization',
        description: 'Optimize your Shopify products for better search rankings'
      };
    }

    return {
      title: `${route.title} - ProdRankX`,
      description: route.description || route.title,
      canonical: route.path
    };
  }

  /**
   * Check if route requires specific permissions
   */
  getRouteRequirements(routeName: string): {
    requiresAuth: boolean;
    requiresBilling: boolean;
    allowedMethods: string[];
  } {
    const route = this.getRoute(routeName);
    if (!route) {
      return {
        requiresAuth: true,
        requiresBilling: false,
        allowedMethods: ['GET']
      };
    }

    return {
      requiresAuth: route.requiresAuth,
      requiresBilling: route.requiresBilling || false,
      allowedMethods: route.allowedMethods
    };
  }

  /**
   * Private helper methods
   */
  private formatSegmentName(segment: string): string {
    return segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }
}

/**
 * Route validation middleware
 */
export async function validateRoute(
  request: Request,
  routeName: string,
  context: {
    isAuthenticated: boolean;
    hasBilling: boolean;
  }
): Promise<void> {
  const routingService = RoutingService.getInstance();
  const method = request.method;

  const validation = routingService.validateRouteAccess(routeName, {
    ...context,
    method
  });

  if (!validation.allowed) {
    throw createError('ROUTE_ACCESS_DENIED', {
      action: 'route_validation',
      metadata: {
        route: routeName,
        reason: validation.reason,
        method
      }
    });
  }
}

/**
 * Create standardized error responses
 */
export function createErrorResponse(
  error: Error,
  status: number = 500
): Response {
  const message = status === 500 ? 'Internal Server Error' : error.message;
  
  return new Response(JSON.stringify({
    error: {
      message,
      status,
      timestamp: new Date().toISOString()
    }
  }), {
    status,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// Export singleton instance
export const routingService = RoutingService.getInstance();
