/**
 * Performance Monitoring Service
 * Tracks performance metrics, memory usage, and identifies potential issues
 */

import { performance } from "perf_hooks";
import { createError, logError, type ErrorContext } from "../utils/error-handling.server";

export interface PerformanceMetrics {
  memoryUsage: {
    rss: number;
    heapUsed: number;
    heapTotal: number;
    external: number;
    arrayBuffers: number;
  };
  cpuUsage: {
    user: number;
    system: number;
  };
  eventLoopLag: number;
  uptime: number;
  timestamp: number;
}

export interface PerformanceAlert {
  type: 'memory_leak' | 'high_cpu' | 'slow_response' | 'event_loop_lag';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  metrics: PerformanceMetrics;
  timestamp: Date;
}

export interface RequestMetrics {
  path: string;
  method: string;
  duration: number;
  memoryBefore: number;
  memoryAfter: number;
  timestamp: Date;
  shop?: string;
  userId?: string;
}

/**
 * Performance monitoring and optimization service
 */
export class PerformanceService {
  private static instance: PerformanceService;
  private metrics: PerformanceMetrics[] = [];
  private requestMetrics: RequestMetrics[] = [];
  private alerts: PerformanceAlert[] = [];
  private maxMetricsHistory = 1000;
  private maxRequestHistory = 5000;
  private monitoringInterval?: NodeJS.Timeout;
  private gcForced = false;

  // Thresholds for alerts
  private thresholds = {
    memoryLeakMB: 100, // MB increase over 5 minutes
    highCpuPercent: 80,
    slowResponseMs: 5000,
    eventLoopLagMs: 100
  };

  static getInstance(): PerformanceService {
    if (!PerformanceService.instance) {
      PerformanceService.instance = new PerformanceService();
    }
    return PerformanceService.instance;
  }

  /**
   * Start performance monitoring
   */
  startMonitoring(): void {
    if (this.monitoringInterval) {
      return; // Already monitoring
    }

    console.log('🔍 Starting performance monitoring...');

    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
      this.analyzeMetrics();
      this.cleanupOldData();
    }, 30000); // Collect metrics every 30 seconds

    // Initial collection
    this.collectMetrics();
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
      console.log('⏹️ Performance monitoring stopped');
    }
  }

  /**
   * Track request performance
   */
  trackRequest(
    path: string,
    method: string,
    startTime: number,
    context?: ErrorContext
  ): void {
    try {
      const duration = performance.now() - startTime;
      const memoryAfter = process.memoryUsage().heapUsed;

      const requestMetric: RequestMetrics = {
        path,
        method,
        duration,
        memoryBefore: 0, // Would need to be passed from request start
        memoryAfter,
        timestamp: new Date(),
        shop: context?.shop,
        userId: context?.userId
      };

      this.requestMetrics.push(requestMetric);

      // Check for slow requests
      if (duration > this.thresholds.slowResponseMs) {
        this.createAlert({
          type: 'slow_response',
          severity: duration > this.thresholds.slowResponseMs * 2 ? 'high' : 'medium',
          message: `Slow request detected: ${method} ${path} took ${duration.toFixed(2)}ms`,
          metrics: this.getCurrentMetrics(),
          timestamp: new Date()
        });
      }

      // Limit history size
      if (this.requestMetrics.length > this.maxRequestHistory) {
        this.requestMetrics = this.requestMetrics.slice(-this.maxRequestHistory);
      }

    } catch (error) {
      console.error('❌ Failed to track request performance:', error);
    }
  }

  /**
   * Get current performance metrics
   */
  getCurrentMetrics(): PerformanceMetrics {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      memoryUsage: {
        rss: memoryUsage.rss,
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        external: memoryUsage.external,
        arrayBuffers: memoryUsage.arrayBuffers
      },
      cpuUsage: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      eventLoopLag: this.measureEventLoopLag(),
      uptime: process.uptime(),
      timestamp: Date.now()
    };
  }

  /**
   * Get performance statistics
   */
  getStats(): {
    currentMetrics: PerformanceMetrics;
    averageResponseTime: number;
    slowestRequests: RequestMetrics[];
    memoryTrend: 'increasing' | 'decreasing' | 'stable';
    alertCount: number;
    recentAlerts: PerformanceAlert[];
  } {
    const currentMetrics = this.getCurrentMetrics();
    
    // Calculate average response time
    const recentRequests = this.requestMetrics.slice(-100);
    const averageResponseTime = recentRequests.length > 0
      ? recentRequests.reduce((sum, req) => sum + req.duration, 0) / recentRequests.length
      : 0;

    // Get slowest requests
    const slowestRequests = [...this.requestMetrics]
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10);

    // Analyze memory trend
    const memoryTrend = this.analyzeMemoryTrend();

    // Get recent alerts
    const recentAlerts = this.alerts.slice(-10);

    return {
      currentMetrics,
      averageResponseTime,
      slowestRequests,
      memoryTrend,
      alertCount: this.alerts.length,
      recentAlerts
    };
  }

  /**
   * Force garbage collection if available
   */
  forceGarbageCollection(): boolean {
    try {
      if (global.gc) {
        const beforeMemory = process.memoryUsage().heapUsed;
        global.gc();
        const afterMemory = process.memoryUsage().heapUsed;
        const freed = beforeMemory - afterMemory;
        
        console.log(`🗑️ Forced GC: freed ${(freed / 1024 / 1024).toFixed(2)} MB`);
        this.gcForced = true;
        return true;
      } else {
        console.warn('⚠️ Garbage collection not available (run with --expose-gc)');
        return false;
      }
    } catch (error) {
      console.error('❌ Failed to force garbage collection:', error);
      return false;
    }
  }

  /**
   * Optimize memory usage
   */
  optimizeMemory(): void {
    try {
      console.log('🔧 Starting memory optimization...');

      // Clear old metrics
      if (this.metrics.length > this.maxMetricsHistory) {
        this.metrics = this.metrics.slice(-Math.floor(this.maxMetricsHistory / 2));
      }

      // Clear old request metrics
      if (this.requestMetrics.length > this.maxRequestHistory) {
        this.requestMetrics = this.requestMetrics.slice(-Math.floor(this.maxRequestHistory / 2));
      }

      // Clear old alerts
      if (this.alerts.length > 100) {
        this.alerts = this.alerts.slice(-50);
      }

      // Force garbage collection
      this.forceGarbageCollection();

      console.log('✅ Memory optimization completed');

    } catch (error) {
      console.error('❌ Memory optimization failed:', error);
    }
  }

  /**
   * Private helper methods
   */
  private collectMetrics(): void {
    try {
      const metrics = this.getCurrentMetrics();
      this.metrics.push(metrics);

      // Limit history size
      if (this.metrics.length > this.maxMetricsHistory) {
        this.metrics = this.metrics.slice(-this.maxMetricsHistory);
      }

    } catch (error) {
      console.error('❌ Failed to collect metrics:', error);
    }
  }

  private analyzeMetrics(): void {
    if (this.metrics.length < 2) {
      return;
    }

    const current = this.metrics[this.metrics.length - 1];
    const previous = this.metrics[this.metrics.length - 2];

    // Check for memory leaks
    const memoryIncrease = current.memoryUsage.heapUsed - previous.memoryUsage.heapUsed;
    const memoryIncreaseMB = memoryIncrease / 1024 / 1024;

    if (memoryIncreaseMB > this.thresholds.memoryLeakMB) {
      this.createAlert({
        type: 'memory_leak',
        severity: memoryIncreaseMB > this.thresholds.memoryLeakMB * 2 ? 'critical' : 'high',
        message: `Potential memory leak detected: ${memoryIncreaseMB.toFixed(2)} MB increase`,
        metrics: current,
        timestamp: new Date()
      });
    }

    // Check event loop lag
    if (current.eventLoopLag > this.thresholds.eventLoopLagMs) {
      this.createAlert({
        type: 'event_loop_lag',
        severity: current.eventLoopLag > this.thresholds.eventLoopLagMs * 2 ? 'high' : 'medium',
        message: `High event loop lag detected: ${current.eventLoopLag.toFixed(2)}ms`,
        metrics: current,
        timestamp: new Date()
      });
    }
  }

  private analyzeMemoryTrend(): 'increasing' | 'decreasing' | 'stable' {
    if (this.metrics.length < 10) {
      return 'stable';
    }

    const recent = this.metrics.slice(-10);
    const first = recent[0].memoryUsage.heapUsed;
    const last = recent[recent.length - 1].memoryUsage.heapUsed;
    const change = (last - first) / first;

    if (change > 0.1) return 'increasing';
    if (change < -0.1) return 'decreasing';
    return 'stable';
  }

  private measureEventLoopLag(): number {
    const start = performance.now();
    return new Promise<number>((resolve) => {
      setImmediate(() => {
        resolve(performance.now() - start);
      });
    }) as any; // Simplified for synchronous use
  }

  private createAlert(alert: PerformanceAlert): void {
    this.alerts.push(alert);
    
    // Log critical alerts
    if (alert.severity === 'critical') {
      console.error(`🚨 CRITICAL PERFORMANCE ALERT: ${alert.message}`);
    } else if (alert.severity === 'high') {
      console.warn(`⚠️ HIGH PERFORMANCE ALERT: ${alert.message}`);
    }

    // Auto-optimize on critical memory issues
    if (alert.type === 'memory_leak' && alert.severity === 'critical') {
      console.log('🔧 Auto-triggering memory optimization due to critical alert');
      this.optimizeMemory();
    }
  }

  private cleanupOldData(): void {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    
    // Remove old metrics
    this.metrics = this.metrics.filter(m => m.timestamp > oneHourAgo);
    
    // Remove old request metrics
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    this.requestMetrics = this.requestMetrics.filter(r => r.timestamp.getTime() > oneDayAgo);
  }
}

// Export singleton instance
export const performanceService = PerformanceService.getInstance();

// Auto-start monitoring in production
if (process.env.NODE_ENV === 'production') {
  performanceService.startMonitoring();
}

// Schedule periodic memory optimization
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    performanceService.optimizeMemory();
  }, 30 * 60 * 1000); // Every 30 minutes
}
