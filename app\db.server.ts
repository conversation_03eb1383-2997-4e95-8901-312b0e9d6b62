import { PrismaClient } from "@prisma/client";
import { getTursoPrismaAdapter } from "./lib/turso-prisma-adapter.server";

declare global {
  var prismaGlobal: EnhancedPrismaClient | undefined;
}

// Enhanced Prisma client with proper connection handling
class EnhancedPrismaClient extends PrismaClient {
  private static instance: EnhancedPrismaClient;
  private connectionAttempts = 0;
  private maxRetries = 3;

  constructor() {
    // In production, we'll use the database abstraction layer
    if (process.env.NODE_ENV === 'production' && process.env.DATABASE_URL?.startsWith('libsql://')) {
      // For production with Turso, we'll proxy through our database layer
      super({
        log: ['error'],
        errorFormat: 'pretty',
      });
    } else {
      // Development with SQLite
      super({
        log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
        errorFormat: 'pretty',
      });
    }
  }

  static getInstance(): EnhancedPrismaClient {
    if (!EnhancedPrismaClient.instance) {
      EnhancedPrismaClient.instance = new EnhancedPrismaClient();
    }
    return EnhancedPrismaClient.instance;
  }

  async connectWithRetry(): Promise<void> {
    try {
      await this.$connect();
      console.log('✅ Database connected successfully');
      this.connectionAttempts = 0;
    } catch (error) {
      this.connectionAttempts++;
      console.error(`❌ Database connection attempt ${this.connectionAttempts} failed:`, error);

      if (this.connectionAttempts < this.maxRetries) {
        const delay = Math.pow(2, this.connectionAttempts) * 1000; // Exponential backoff
        console.log(`⏳ Retrying connection in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.connectWithRetry();
      } else {
        throw new Error(`Failed to connect to database after ${this.maxRetries} attempts`);
      }
    }
  }

  async safeQuery<T>(operation: () => Promise<T>): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      console.error('❌ Database query failed:', error);

      // Check if it's a connection error and retry once
      if (error instanceof Error && error.message.includes('connection')) {
        console.log('🔄 Attempting to reconnect and retry query...');
        await this.connectWithRetry();
        return await operation();
      }

      throw error;
    }
  }
}

// Global instance management with production-grade database layer
let prisma: any;

// Debug environment variables
console.log('🔍 Database Environment Debug:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('TURSO_DATABASE_URL:', process.env.TURSO_DATABASE_URL ? 'present' : 'missing');
console.log('TURSO_AUTH_TOKEN:', process.env.TURSO_AUTH_TOKEN ? 'present' : 'missing');
console.log('DATABASE_URL:', process.env.DATABASE_URL ? process.env.DATABASE_URL.substring(0, 20) + '...' : 'missing');

if (process.env.NODE_ENV === "production" && process.env.TURSO_DATABASE_URL && process.env.TURSO_AUTH_TOKEN) {
  // Production with Turso: Use our Turso-Prisma adapter
  console.log('🚀 Initializing production database with Turso');
  prisma = getTursoPrismaAdapter();
} else if (process.env.NODE_ENV === "production") {
  // Production without Turso: CRITICAL ERROR - This will fail on serverless
  console.error('❌ CRITICAL: Production deployment missing Turso credentials!');
  console.error('❌ Required environment variables:');
  console.error('   - TURSO_DATABASE_URL');
  console.error('   - TURSO_AUTH_TOKEN');
  console.error('❌ Deployment will fail without these credentials!');

  // Still try to use Turso adapter as fallback (will handle missing credentials gracefully)
  console.log('⚠️ Attempting to use Turso adapter without credentials (will likely fail)');
  prisma = getTursoPrismaAdapter();
} else {
  // Development: Use regular Prisma with SQLite
  console.log('🔧 Development mode - using local SQLite');
  if (!global.prismaGlobal) {
    global.prismaGlobal = EnhancedPrismaClient.getInstance();
  }
  prisma = global.prismaGlobal;
}

// Initialize connection
if (prisma.connectWithRetry) {
  prisma.connectWithRetry().catch((error: any) => {
    console.error('❌ Failed to initialize database connection:', error);
  });
} else if (prisma.$connect) {
  prisma.$connect().catch((error: any) => {
    console.error('❌ Failed to initialize database connection:', error);
  });
}

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('🛑 Received SIGINT, closing database connection...');
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('🛑 Received SIGTERM, closing database connection...');
  await prisma.$disconnect();
  process.exit(0);
});

export default prisma;
