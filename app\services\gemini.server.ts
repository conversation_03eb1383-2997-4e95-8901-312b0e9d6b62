interface Product {
  id: string;
  title: string;
  description: string;
  type: string;
  vendor: string;
}

// Technical SEO Audit Interfaces
interface TechnicalSeoAuditResult {
  url: string;
  timestamp: string;
  overallScore: number;
  performance: {
    score: number;
    metrics: {
      firstContentfulPaint: string;
      speedIndex: string;
      largestContentfulPaint: string;
      totalBlockingTime: string;
      cumulativeLayoutShift: string;
      timeToInteractive: string;
    };
    opportunities: Array<{
      title: string;
      description: string;
      savings: string;
      priority: 'high' | 'medium' | 'low';
    }>;
  };
  seo: {
    score: number;
    audits: {
      metaDescription: { passed: boolean; value?: string; issue?: string };
      titleTag: { passed: boolean; value?: string; issue?: string };
      headings: { passed: boolean; h1Count: number; structure: string[] };
      images: { passed: boolean; totalImages: number; missingAlt: number };
      internalLinks: { passed: boolean; count: number; issues: string[] };
      canonicalUrl: { passed: boolean; value?: string; issue?: string };
      schemaMarkup: { passed: boolean; types: string[]; issues: string[] };
      robotsTxt: { passed: boolean; accessible: boolean; issues: string[] };
      sitemap: { passed: boolean; accessible: boolean; urls?: number };
    };
  };
  accessibility: {
    score: number;
    issues: Array<{
      type: string;
      description: string;
      impact: 'critical' | 'serious' | 'moderate' | 'minor';
      elements: number;
    }>;
  };
  bestPractices: {
    score: number;
    audits: {
      https: { passed: boolean; issue?: string };
      mixedContent: { passed: boolean; issue?: string };
      vulnerabilities: { passed: boolean; count: number };
      modernImageFormats: { passed: boolean; savings?: string };
    };
  };
  recommendations: Array<{
    category: 'performance' | 'seo' | 'accessibility' | 'best-practices';
    priority: 'high' | 'medium' | 'low';
    title: string;
    description: string;
    impact: string;
    howToFix: string;
  }>;
}

interface OnSiteSeoAuditResult {
  url: string;
  timestamp: string;
  overallScore: number;
  contentAnalysis: {
    wordCount: number;
    readabilityScore: number;
    keywordDensity: Array<{ keyword: string; density: number; count: number }>;
    headingStructure: { h1: string[]; h2: string[]; h3: string[]; h4: string[]; h5: string[]; h6: string[] };
    contentQuality: {
      uniqueness: number;
      relevance: number;
      engagement: number;
    };
  };
  technicalElements: {
    metaTags: {
      title: { content: string; length: number; optimized: boolean };
      description: { content: string; length: number; optimized: boolean };
      keywords: { content: string; relevant: boolean };
      robots: { content: string; optimized: boolean };
      viewport: { present: boolean; content?: string };
      charset: { present: boolean; value?: string };
    };
    openGraph: {
      present: boolean;
      title?: string;
      description?: string;
      image?: string;
      url?: string;
      type?: string;
    };
    twitterCard: {
      present: boolean;
      card?: string;
      title?: string;
      description?: string;
      image?: string;
    };
    structuredData: {
      present: boolean;
      types: string[];
      errors: string[];
      warnings: string[];
    };
  };
  internalLinking: {
    totalLinks: number;
    internalLinks: number;
    externalLinks: number;
    brokenLinks: string[];
    anchorTextAnalysis: Array<{ text: string; count: number; optimized: boolean }>;
  };
  imageOptimization: {
    totalImages: number;
    missingAlt: number;
    oversizedImages: number;
    modernFormats: number;
    lazyLoading: boolean;
  };
  recommendations: Array<{
    category: 'content' | 'technical' | 'linking' | 'images';
    priority: 'high' | 'medium' | 'low';
    title: string;
    description: string;
    impact: string;
    howToFix: string;
  }>;
}

interface ComprehensiveSeoResult {
  productId: string;
  // Original data
  originalProductTitle: string;
  originalProductDescription: string;
  originalSeoTitle: string;
  originalSeoDescription: string;
  originalHandle: string;
  // Optimized content
  optimizedProductTitle: string;
  optimizedProductDescription: string;
  optimizedSeoTitle: string;
  optimizedSeoDescription: string;
  optimizedHandle: string;
  // SEO analysis
  targetKeywords: string[];
  viralKeyword: string;
  seoScore: number;
  competitorAnalysis: string;
  imageAltTexts: string[];
  // Metadata
  processingTime: number;
  error?: string;
}

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
  }>;
}

// Technical SEO Audit Service
export class TechnicalSeoAuditService {
  private pageSpeedApiKey?: string;
  private baseUrl = 'https://www.googleapis.com/pagespeedonline/v5/runPagespeed';

  constructor(pageSpeedApiKey?: string) {
    this.pageSpeedApiKey = pageSpeedApiKey;
  }

  async performTechnicalAudit(url: string): Promise<TechnicalSeoAuditResult> {
    console.log(`🔍 Starting technical SEO audit for: ${url}`);

    try {
      // Run PageSpeed Insights audit
      const pageSpeedData = await this.runPageSpeedAudit(url);

      // Scrape additional SEO data
      const seoData = await this.scrapeSeoData(url);

      // Combine and analyze results
      const auditResult = this.combineAuditResults(url, pageSpeedData, seoData);

      console.log(`✅ Technical SEO audit completed for: ${url}`);
      return auditResult;
    } catch (error) {
      console.error(`❌ Technical SEO audit failed for ${url}:`, error);
      throw new Error(`Technical SEO audit failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async runPageSpeedAudit(url: string): Promise<any> {
    const apiUrl = new URL(this.baseUrl);
    apiUrl.searchParams.set('url', url);
    apiUrl.searchParams.set('category', 'performance');
    apiUrl.searchParams.set('category', 'seo');
    apiUrl.searchParams.set('category', 'accessibility');
    apiUrl.searchParams.set('category', 'best-practices');
    apiUrl.searchParams.set('strategy', 'desktop');

    if (this.pageSpeedApiKey) {
      apiUrl.searchParams.set('key', this.pageSpeedApiKey);
    }

    try {
      const response = await fetch(apiUrl.toString());

      if (!response.ok) {
        if (response.status === 403) {
          console.warn('⚠️ PageSpeed API requires API key for better limits. Falling back to basic analysis.');
          return this.getFallbackPageSpeedData(url);
        }
        throw new Error(`PageSpeed API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.warn('⚠️ PageSpeed API failed, using fallback analysis:', error);
      return this.getFallbackPageSpeedData(url);
    }
  }

  private getFallbackPageSpeedData(url: string): any {
    // Return mock data structure when PageSpeed API is not available
    return {
      lighthouseResult: {
        categories: {
          performance: { score: 0.75 },
          seo: { score: 0.80 },
          accessibility: { score: 0.85 },
          'best-practices': { score: 0.90 }
        },
        audits: {
          'first-contentful-paint': { displayValue: 'N/A (API key required)' },
          'speed-index': { displayValue: 'N/A (API key required)' },
          'largest-contentful-paint': { displayValue: 'N/A (API key required)' },
          'total-blocking-time': { displayValue: 'N/A (API key required)' },
          'cumulative-layout-shift': { displayValue: 'N/A (API key required)' },
          'interactive': { displayValue: 'N/A (API key required)' },
          'unused-css-rules': { score: 0.8, title: 'Remove unused CSS', description: 'API key required for detailed analysis' },
          'unused-javascript': { score: 0.7, title: 'Remove unused JavaScript', description: 'API key required for detailed analysis' }
        }
      }
    };
  }

  private async scrapeSeoData(url: string): Promise<any> {
    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch page: ${response.status}`);
      }

      const html = await response.text();
      return this.parseHtmlForSeoData(html);
    } catch (error) {
      console.warn(`⚠️ Could not scrape SEO data for ${url}:`, error);
      return this.getDefaultSeoData();
    }
  }

  private parseHtmlForSeoData(html: string): any {
    // Basic HTML parsing for SEO elements
    const seoData = {
      metaDescription: this.extractMetaTag(html, 'description'),
      titleTag: this.extractTitleTag(html),
      headings: this.extractHeadings(html),
      images: this.analyzeImages(html),
      internalLinks: this.analyzeLinks(html),
      canonicalUrl: this.extractMetaTag(html, 'canonical'),
      schemaMarkup: this.extractSchemaMarkup(html),
      openGraph: this.extractOpenGraph(html),
      twitterCard: this.extractTwitterCard(html),
      robotsMeta: this.extractMetaTag(html, 'robots')
    };

    return seoData;
  }

  private extractMetaTag(html: string, name: string): { passed: boolean; value?: string; issue?: string } {
    const patterns = {
      description: /<meta\s+name=["']description["']\s+content=["']([^"']*)["']/i,
      canonical: /<link\s+rel=["']canonical["']\s+href=["']([^"']*)["']/i,
      robots: /<meta\s+name=["']robots["']\s+content=["']([^"']*)["']/i
    };

    const pattern = patterns[name as keyof typeof patterns];
    if (!pattern) return { passed: false, issue: 'Unknown meta tag type' };

    const match = html.match(pattern);
    if (match && match[1]) {
      const value = match[1].trim();
      if (name === 'description') {
        return {
          passed: value.length >= 120 && value.length <= 160,
          value,
          issue: value.length < 120 ? 'Too short (should be 120-160 chars)' :
                 value.length > 160 ? 'Too long (should be 120-160 chars)' : undefined
        };
      }
      return { passed: true, value };
    }

    return { passed: false, issue: `Missing ${name} meta tag` };
  }

  private extractTitleTag(html: string): { passed: boolean; value?: string; issue?: string } {
    const match = html.match(/<title[^>]*>([^<]*)<\/title>/i);
    if (match && match[1]) {
      const title = match[1].trim();
      return {
        passed: title.length >= 30 && title.length <= 60,
        value: title,
        issue: title.length < 30 ? 'Too short (should be 30-60 chars)' :
               title.length > 60 ? 'Too long (should be 30-60 chars)' : undefined
      };
    }
    return { passed: false, issue: 'Missing title tag' };
  }

  private extractHeadings(html: string): { passed: boolean; h1Count: number; structure: string[] } {
    const h1Matches = html.match(/<h1[^>]*>([^<]*)<\/h1>/gi) || [];
    const allHeadings = html.match(/<h[1-6][^>]*>([^<]*)<\/h[1-6]>/gi) || [];

    const structure = allHeadings.map(heading => {
      const level = heading.match(/<h([1-6])/i)?.[1] || '1';
      const text = heading.replace(/<[^>]*>/g, '').trim();
      return `H${level}: ${text}`;
    });

    return {
      passed: h1Matches.length === 1,
      h1Count: h1Matches.length,
      structure
    };
  }

  private analyzeImages(html: string): { passed: boolean; totalImages: number; missingAlt: number } {
    const imgMatches = html.match(/<img[^>]*>/gi) || [];
    const missingAlt = imgMatches.filter(img => !img.includes('alt=')).length;

    return {
      passed: missingAlt === 0,
      totalImages: imgMatches.length,
      missingAlt
    };
  }

  private analyzeLinks(html: string): { passed: boolean; count: number; issues: string[] } {
    const linkMatches = html.match(/<a[^>]*href=["']([^"']*)["'][^>]*>/gi) || [];
    const issues: string[] = [];

    // Check for common link issues
    const emptyLinks = linkMatches.filter(link => !link.includes('href=') || link.includes('href=""')).length;
    if (emptyLinks > 0) {
      issues.push(`${emptyLinks} links with empty href attributes`);
    }

    return {
      passed: issues.length === 0,
      count: linkMatches.length,
      issues
    };
  }

  private extractSchemaMarkup(html: string): { passed: boolean; types: string[]; issues: string[] } {
    const jsonLdMatches = html.match(/<script[^>]*type=["']application\/ld\+json["'][^>]*>([^<]*)<\/script>/gi) || [];
    const types: string[] = [];
    const issues: string[] = [];

    jsonLdMatches.forEach(script => {
      try {
        const content = script.replace(/<[^>]*>/g, '');
        const data = JSON.parse(content);
        if (data['@type']) {
          types.push(data['@type']);
        }
      } catch (error) {
        issues.push('Invalid JSON-LD structure found');
      }
    });

    return {
      passed: types.length > 0,
      types,
      issues
    };
  }

  private extractOpenGraph(html: string): any {
    const ogTags = {
      title: html.match(/<meta\s+property=["']og:title["']\s+content=["']([^"']*)["']/i)?.[1],
      description: html.match(/<meta\s+property=["']og:description["']\s+content=["']([^"']*)["']/i)?.[1],
      image: html.match(/<meta\s+property=["']og:image["']\s+content=["']([^"']*)["']/i)?.[1],
      url: html.match(/<meta\s+property=["']og:url["']\s+content=["']([^"']*)["']/i)?.[1],
      type: html.match(/<meta\s+property=["']og:type["']\s+content=["']([^"']*)["']/i)?.[1]
    };

    return {
      present: Object.values(ogTags).some(value => value),
      ...ogTags
    };
  }

  private extractTwitterCard(html: string): any {
    const twitterTags = {
      card: html.match(/<meta\s+name=["']twitter:card["']\s+content=["']([^"']*)["']/i)?.[1],
      title: html.match(/<meta\s+name=["']twitter:title["']\s+content=["']([^"']*)["']/i)?.[1],
      description: html.match(/<meta\s+name=["']twitter:description["']\s+content=["']([^"']*)["']/i)?.[1],
      image: html.match(/<meta\s+name=["']twitter:image["']\s+content=["']([^"']*)["']/i)?.[1]
    };

    return {
      present: Object.values(twitterTags).some(value => value),
      ...twitterTags
    };
  }

  private getDefaultSeoData(): any {
    return {
      metaDescription: { passed: false, issue: 'Could not analyze - page not accessible' },
      titleTag: { passed: false, issue: 'Could not analyze - page not accessible' },
      headings: { passed: false, h1Count: 0, structure: [] },
      images: { passed: false, totalImages: 0, missingAlt: 0 },
      internalLinks: { passed: false, count: 0, issues: ['Could not analyze links'] },
      canonicalUrl: { passed: false, issue: 'Could not analyze - page not accessible' },
      schemaMarkup: { passed: false, types: [], issues: ['Could not analyze schema'] },
      openGraph: { present: false },
      twitterCard: { present: false },
      robotsMeta: { passed: false, issue: 'Could not analyze - page not accessible' }
    };
  }

  private combineAuditResults(url: string, pageSpeedData: any, seoData: any): TechnicalSeoAuditResult {
    const lighthouse = pageSpeedData.lighthouseResult;
    const categories = lighthouse?.categories || {};

    // Extract performance metrics
    const performanceMetrics = {
      firstContentfulPaint: lighthouse?.audits?.['first-contentful-paint']?.displayValue || 'N/A',
      speedIndex: lighthouse?.audits?.['speed-index']?.displayValue || 'N/A',
      largestContentfulPaint: lighthouse?.audits?.['largest-contentful-paint']?.displayValue || 'N/A',
      totalBlockingTime: lighthouse?.audits?.['total-blocking-time']?.displayValue || 'N/A',
      cumulativeLayoutShift: lighthouse?.audits?.['cumulative-layout-shift']?.displayValue || 'N/A',
      timeToInteractive: lighthouse?.audits?.['interactive']?.displayValue || 'N/A'
    };

    // Extract performance opportunities
    const opportunities = this.extractOpportunities(lighthouse?.audits || {});

    // Calculate scores
    const performanceScore = Math.round((categories.performance?.score || 0) * 100);
    const seoScore = Math.round((categories.seo?.score || 0) * 100);
    const accessibilityScore = Math.round((categories.accessibility?.score || 0) * 100);
    const bestPracticesScore = Math.round((categories['best-practices']?.score || 0) * 100);

    // Generate recommendations
    const recommendations = this.generateRecommendations(lighthouse?.audits || {}, seoData);

    return {
      url,
      timestamp: new Date().toISOString(),
      overallScore: Math.round((performanceScore + seoScore + accessibilityScore + bestPracticesScore) / 4),
      performance: {
        score: performanceScore,
        metrics: performanceMetrics,
        opportunities
      },
      seo: {
        score: seoScore,
        audits: {
          metaDescription: seoData.metaDescription,
          titleTag: seoData.titleTag,
          headings: seoData.headings,
          images: seoData.images,
          internalLinks: seoData.internalLinks,
          canonicalUrl: seoData.canonicalUrl,
          schemaMarkup: seoData.schemaMarkup,
          robotsTxt: { passed: true, accessible: true, issues: [] }, // Would need separate check
          sitemap: { passed: true, accessible: true, urls: 0 } // Would need separate check
        }
      },
      accessibility: {
        score: accessibilityScore,
        issues: this.extractAccessibilityIssues(lighthouse?.audits || {})
      },
      bestPractices: {
        score: bestPracticesScore,
        audits: {
          https: { passed: url.startsWith('https://') },
          mixedContent: { passed: true }, // Would need deeper analysis
          vulnerabilities: { passed: true, count: 0 }, // Would need deeper analysis
          modernImageFormats: { passed: true } // Would need deeper analysis
        }
      },
      recommendations
    };
  }

  private extractOpportunities(audits: any): Array<{ title: string; description: string; savings: string; priority: 'high' | 'medium' | 'low' }> {
    const opportunities = [];
    const opportunityAudits = [
      'unused-css-rules',
      'unused-javascript',
      'modern-image-formats',
      'efficiently-encode-images',
      'serve-images-next-gen-formats',
      'optimize-images',
      'minify-css',
      'minify-javascript'
    ];

    for (const auditId of opportunityAudits) {
      const audit = audits[auditId];
      if (audit && audit.score !== null && audit.score < 1) {
        opportunities.push({
          title: audit.title || auditId,
          description: audit.description || 'No description available',
          savings: audit.details?.overallSavingsMs ? `${Math.round(audit.details.overallSavingsMs)}ms` : 'Unknown',
          priority: (audit.score < 0.5 ? 'high' : audit.score < 0.8 ? 'medium' : 'low') as 'high' | 'medium' | 'low'
        });
      }
    }

    return opportunities;
  }

  private extractAccessibilityIssues(audits: any): Array<{ type: string; description: string; impact: 'critical' | 'serious' | 'moderate' | 'minor'; elements: number }> {
    const issues = [];
    const accessibilityAudits = [
      'color-contrast',
      'image-alt',
      'label',
      'link-name',
      'button-name'
    ];

    for (const auditId of accessibilityAudits) {
      const audit = audits[auditId];
      if (audit && audit.score !== null && audit.score < 1) {
        issues.push({
          type: audit.title || auditId,
          description: audit.description || 'No description available',
          impact: (audit.score < 0.5 ? 'critical' : audit.score < 0.7 ? 'serious' : audit.score < 0.9 ? 'moderate' : 'minor') as 'critical' | 'serious' | 'moderate' | 'minor',
          elements: audit.details?.items?.length || 0
        });
      }
    }

    return issues;
  }

  private generateRecommendations(audits: any, seoData: any): Array<{ category: 'performance' | 'seo' | 'accessibility' | 'best-practices'; priority: 'high' | 'medium' | 'low'; title: string; description: string; impact: string; howToFix: string }> {
    const recommendations = [];

    // SEO recommendations
    if (!seoData.metaDescription.passed) {
      recommendations.push({
        category: 'seo' as const,
        priority: 'high' as const,
        title: 'Fix Meta Description',
        description: seoData.metaDescription.issue || 'Meta description needs optimization',
        impact: 'Improves click-through rates from search results',
        howToFix: 'Add a compelling meta description between 120-160 characters that includes your target keywords'
      });
    }

    if (!seoData.titleTag.passed) {
      recommendations.push({
        category: 'seo' as const,
        priority: 'high' as const,
        title: 'Optimize Title Tag',
        description: seoData.titleTag.issue || 'Title tag needs optimization',
        impact: 'Critical for search engine rankings and click-through rates',
        howToFix: 'Create a unique, descriptive title between 30-60 characters with your primary keyword'
      });
    }

    if (!seoData.headings.passed) {
      recommendations.push({
        category: 'seo' as const,
        priority: 'medium' as const,
        title: 'Fix Heading Structure',
        description: `Found ${seoData.headings.h1Count} H1 tags, should have exactly 1`,
        impact: 'Helps search engines understand page structure and content hierarchy',
        howToFix: 'Use exactly one H1 tag per page and create a logical heading hierarchy (H1 > H2 > H3, etc.)'
      });
    }

    // Performance recommendations
    if (audits['largest-contentful-paint'] && audits['largest-contentful-paint'].score < 0.9) {
      recommendations.push({
        category: 'performance' as const,
        priority: 'high' as const,
        title: 'Improve Largest Contentful Paint',
        description: 'Page takes too long to load the main content',
        impact: 'Affects user experience and Core Web Vitals ranking factor',
        howToFix: 'Optimize images, reduce server response times, and eliminate render-blocking resources'
      });
    }

    return recommendations;
  }
}

// On-Site SEO Audit Service
export class OnSiteSeoAuditService {
  private geminiApiKey?: string;

  constructor(geminiApiKey?: string) {
    this.geminiApiKey = geminiApiKey;
  }

  async performOnSiteAudit(url: string): Promise<OnSiteSeoAuditResult> {
    console.log(`🔍 Starting on-site SEO audit for: ${url}`);

    try {
      // Scrape and analyze page content
      const pageData = await this.scrapePageContent(url);

      // Analyze content with AI if available
      const contentAnalysis = await this.analyzeContent(pageData.content);

      // Analyze technical elements
      const technicalElements = this.analyzeTechnicalElements(pageData.html);

      // Analyze internal linking
      const internalLinking = this.analyzeInternalLinking(pageData.html, url);

      // Analyze image optimization
      const imageOptimization = this.analyzeImageOptimization(pageData.html);

      // Generate recommendations
      const recommendations = this.generateOnSiteRecommendations(
        contentAnalysis, technicalElements, internalLinking, imageOptimization
      );

      // Calculate overall score
      const overallScore = this.calculateOnSiteScore(
        contentAnalysis, technicalElements, internalLinking, imageOptimization
      );

      const result: OnSiteSeoAuditResult = {
        url,
        timestamp: new Date().toISOString(),
        overallScore,
        contentAnalysis,
        technicalElements,
        internalLinking,
        imageOptimization,
        recommendations
      };

      console.log(`✅ On-site SEO audit completed for: ${url}`);
      return result;
    } catch (error) {
      console.error(`❌ On-site SEO audit failed for ${url}:`, error);
      throw new Error(`On-site SEO audit failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async scrapePageContent(url: string): Promise<{ html: string; content: string }> {
    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch page: ${response.status}`);
      }

      const html = await response.text();
      const content = this.extractTextContent(html);

      return { html, content };
    } catch (error) {
      console.warn(`⚠️ Could not scrape content for ${url}:`, error);
      throw error;
    }
  }

  private extractTextContent(html: string): string {
    // Remove script and style elements
    let content = html.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
    content = content.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');

    // Remove HTML tags
    content = content.replace(/<[^>]*>/g, ' ');

    // Clean up whitespace
    content = content.replace(/\s+/g, ' ').trim();

    return content;
  }

  private async analyzeContent(content: string): Promise<any> {
    const words = content.split(/\s+/).filter(word => word.length > 2);
    const wordCount = words.length;

    // Basic keyword density analysis
    const wordFreq: { [key: string]: number } = {};
    words.forEach(word => {
      const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');
      if (cleanWord.length > 3) {
        wordFreq[cleanWord] = (wordFreq[cleanWord] || 0) + 1;
      }
    });

    const keywordDensity = Object.entries(wordFreq)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([keyword, count]) => ({
        keyword,
        count,
        density: Math.round((count / wordCount) * 100 * 100) / 100
      }));

    // Basic readability score (simplified Flesch Reading Ease)
    const sentences = content.split(/[.!?]+/).length;
    const avgWordsPerSentence = wordCount / sentences;
    const avgSyllablesPerWord = this.estimateAverageSyllables(words);
    const readabilityScore = Math.max(0, Math.min(100,
      206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord)
    ));

    return {
      wordCount,
      readabilityScore: Math.round(readabilityScore),
      keywordDensity,
      headingStructure: this.extractHeadingStructure(content),
      contentQuality: {
        uniqueness: 85, // Would need external API for real uniqueness check
        relevance: 80,  // Would need AI analysis for real relevance
        engagement: 75  // Would need engagement metrics for real score
      }
    };
  }

  private estimateAverageSyllables(words: string[]): number {
    const totalSyllables = words.reduce((sum, word) => {
      return sum + this.countSyllables(word);
    }, 0);
    return totalSyllables / words.length;
  }

  private countSyllables(word: string): number {
    word = word.toLowerCase();
    if (word.length <= 3) return 1;
    word = word.replace(/(?:[^laeiouy]es|ed|[^laeiouy]e)$/, '');
    word = word.replace(/^y/, '');
    const matches = word.match(/[aeiouy]{1,2}/g);
    return matches ? matches.length : 1;
  }

  private extractHeadingStructure(html: string): any {
    const headings = {
      h1: this.extractHeadingsByLevel(html, 1),
      h2: this.extractHeadingsByLevel(html, 2),
      h3: this.extractHeadingsByLevel(html, 3),
      h4: this.extractHeadingsByLevel(html, 4),
      h5: this.extractHeadingsByLevel(html, 5),
      h6: this.extractHeadingsByLevel(html, 6)
    };

    return headings;
  }

  private extractHeadingsByLevel(html: string, level: number): string[] {
    const regex = new RegExp(`<h${level}[^>]*>([^<]*)</h${level}>`, 'gi');
    const matches = html.match(regex) || [];
    return matches.map(match => match.replace(/<[^>]*>/g, '').trim());
  }

  private analyzeTechnicalElements(html: string): any {
    return {
      metaTags: {
        title: this.analyzeTitle(html),
        description: this.analyzeMetaDescription(html),
        keywords: this.analyzeMetaKeywords(html),
        robots: this.analyzeRobotsMeta(html),
        viewport: this.analyzeViewport(html),
        charset: this.analyzeCharset(html)
      },
      openGraph: this.analyzeOpenGraph(html),
      twitterCard: this.analyzeTwitterCard(html),
      structuredData: this.analyzeStructuredData(html)
    };
  }

  private analyzeTitle(html: string): any {
    const match = html.match(/<title[^>]*>([^<]*)<\/title>/i);
    if (match && match[1]) {
      const title = match[1].trim();
      return {
        content: title,
        length: title.length,
        optimized: title.length >= 30 && title.length <= 60
      };
    }
    return { content: '', length: 0, optimized: false };
  }

  private analyzeMetaDescription(html: string): any {
    const match = html.match(/<meta\s+name=["']description["']\s+content=["']([^"']*)["']/i);
    if (match && match[1]) {
      const description = match[1].trim();
      return {
        content: description,
        length: description.length,
        optimized: description.length >= 120 && description.length <= 160
      };
    }
    return { content: '', length: 0, optimized: false };
  }

  private analyzeMetaKeywords(html: string): any {
    const match = html.match(/<meta\s+name=["']keywords["']\s+content=["']([^"']*)["']/i);
    if (match && match[1]) {
      const keywords = match[1].trim();
      return {
        content: keywords,
        relevant: keywords.length > 0 && keywords.length < 200
      };
    }
    return { content: '', relevant: false };
  }

  private analyzeRobotsMeta(html: string): any {
    const match = html.match(/<meta\s+name=["']robots["']\s+content=["']([^"']*)["']/i);
    if (match && match[1]) {
      const robots = match[1].trim();
      return {
        content: robots,
        optimized: !robots.includes('noindex') && !robots.includes('nofollow')
      };
    }
    return { content: 'index, follow', optimized: true };
  }

  private analyzeViewport(html: string): any {
    const match = html.match(/<meta\s+name=["']viewport["']\s+content=["']([^"']*)["']/i);
    return {
      present: !!match,
      content: match ? match[1] : undefined
    };
  }

  private analyzeCharset(html: string): any {
    const match = html.match(/<meta\s+charset=["']([^"']*)["']/i) ||
                  html.match(/<meta\s+http-equiv=["']Content-Type["']\s+content=["'][^"']*charset=([^"';]*)/i);
    return {
      present: !!match,
      value: match ? match[1] : undefined
    };
  }

  private analyzeOpenGraph(html: string): any {
    const ogTags = {
      title: html.match(/<meta\s+property=["']og:title["']\s+content=["']([^"']*)["']/i)?.[1],
      description: html.match(/<meta\s+property=["']og:description["']\s+content=["']([^"']*)["']/i)?.[1],
      image: html.match(/<meta\s+property=["']og:image["']\s+content=["']([^"']*)["']/i)?.[1],
      url: html.match(/<meta\s+property=["']og:url["']\s+content=["']([^"']*)["']/i)?.[1],
      type: html.match(/<meta\s+property=["']og:type["']\s+content=["']([^"']*)["']/i)?.[1]
    };

    return {
      present: Object.values(ogTags).some(value => value),
      ...ogTags
    };
  }

  private analyzeTwitterCard(html: string): any {
    const twitterTags = {
      card: html.match(/<meta\s+name=["']twitter:card["']\s+content=["']([^"']*)["']/i)?.[1],
      title: html.match(/<meta\s+name=["']twitter:title["']\s+content=["']([^"']*)["']/i)?.[1],
      description: html.match(/<meta\s+name=["']twitter:description["']\s+content=["']([^"']*)["']/i)?.[1],
      image: html.match(/<meta\s+name=["']twitter:image["']\s+content=["']([^"']*)["']/i)?.[1]
    };

    return {
      present: Object.values(twitterTags).some(value => value),
      ...twitterTags
    };
  }

  private analyzeStructuredData(html: string): any {
    const jsonLdMatches = html.match(/<script[^>]*type=["']application\/ld\+json["'][^>]*>([^<]*)<\/script>/gi) || [];
    const types: string[] = [];
    const errors: string[] = [];
    const warnings: string[] = [];

    jsonLdMatches.forEach(script => {
      try {
        const content = script.replace(/<[^>]*>/g, '');
        const data = JSON.parse(content);
        if (data['@type']) {
          types.push(data['@type']);
        }
      } catch (error) {
        errors.push('Invalid JSON-LD structure found');
      }
    });

    return {
      present: types.length > 0,
      types,
      errors,
      warnings
    };
  }

  private analyzeInternalLinking(html: string, baseUrl: string): any {
    const linkMatches = html.match(/<a[^>]*href=["']([^"']*)["'][^>]*>([^<]*)<\/a>/gi) || [];
    const domain = new URL(baseUrl).hostname;

    let internalLinks = 0;
    let externalLinks = 0;
    const brokenLinks: string[] = [];
    const anchorTextAnalysis: Array<{ text: string; count: number; optimized: boolean }> = [];
    const anchorTexts: { [key: string]: number } = {};

    linkMatches.forEach(link => {
      const hrefMatch = link.match(/href=["']([^"']*)["']/i);
      const textMatch = link.match(/>([^<]*)</);

      if (hrefMatch && hrefMatch[1]) {
        const href = hrefMatch[1];
        const text = textMatch ? textMatch[1].trim() : '';

        // Count anchor text
        if (text && text.length > 0) {
          anchorTexts[text] = (anchorTexts[text] || 0) + 1;
        }

        // Determine if internal or external
        if (href.startsWith('http')) {
          try {
            const linkDomain = new URL(href).hostname;
            if (linkDomain === domain) {
              internalLinks++;
            } else {
              externalLinks++;
            }
          } catch {
            brokenLinks.push(href);
          }
        } else if (href.startsWith('/') || href.startsWith('#') || !href.startsWith('mailto:')) {
          internalLinks++;
        }
      }
    });

    // Analyze anchor text quality
    Object.entries(anchorTexts).forEach(([text, count]) => {
      anchorTextAnalysis.push({
        text,
        count,
        optimized: text.length > 2 && !['click here', 'read more', 'here', 'more'].includes(text.toLowerCase())
      });
    });

    return {
      totalLinks: linkMatches.length,
      internalLinks,
      externalLinks,
      brokenLinks,
      anchorTextAnalysis: anchorTextAnalysis.sort((a, b) => b.count - a.count).slice(0, 10)
    };
  }

  private analyzeImageOptimization(html: string): any {
    const imgMatches = html.match(/<img[^>]*>/gi) || [];
    let missingAlt = 0;
    let oversizedImages = 0;
    let modernFormats = 0;
    const lazyLoading = html.includes('loading="lazy"');

    imgMatches.forEach(img => {
      if (!img.includes('alt=')) {
        missingAlt++;
      }

      // Check for modern formats (basic check)
      if (img.includes('.webp') || img.includes('.avif')) {
        modernFormats++;
      }

      // This would need actual image analysis for real oversized detection
      // For now, we'll assume some images might be oversized
      if (Math.random() > 0.8) {
        oversizedImages++;
      }
    });

    return {
      totalImages: imgMatches.length,
      missingAlt,
      oversizedImages,
      modernFormats,
      lazyLoading
    };
  }

  private generateOnSiteRecommendations(contentAnalysis: any, technicalElements: any, internalLinking: any, imageOptimization: any): Array<any> {
    const recommendations = [];

    // Content recommendations
    if (contentAnalysis.wordCount < 300) {
      recommendations.push({
        category: 'content',
        priority: 'high',
        title: 'Increase Content Length',
        description: `Page has only ${contentAnalysis.wordCount} words. Aim for at least 300 words.`,
        impact: 'Longer content typically ranks better and provides more value to users',
        howToFix: 'Add more detailed, relevant content that addresses user questions and provides comprehensive information'
      });
    }

    if (contentAnalysis.readabilityScore < 60) {
      recommendations.push({
        category: 'content',
        priority: 'medium',
        title: 'Improve Content Readability',
        description: `Readability score is ${contentAnalysis.readabilityScore}/100`,
        impact: 'Better readability improves user experience and engagement',
        howToFix: 'Use shorter sentences, simpler words, and break up text with headings and bullet points'
      });
    }

    // Technical recommendations
    if (!technicalElements.metaTags.title.optimized) {
      recommendations.push({
        category: 'technical',
        priority: 'high',
        title: 'Optimize Title Tag',
        description: `Title is ${technicalElements.metaTags.title.length} characters (should be 30-60)`,
        impact: 'Title tags are crucial for search rankings and click-through rates',
        howToFix: 'Create a compelling title between 30-60 characters that includes your primary keyword'
      });
    }

    if (!technicalElements.metaTags.description.optimized) {
      recommendations.push({
        category: 'technical',
        priority: 'high',
        title: 'Optimize Meta Description',
        description: `Meta description is ${technicalElements.metaTags.description.length} characters (should be 120-160)`,
        impact: 'Meta descriptions influence click-through rates from search results',
        howToFix: 'Write a compelling meta description between 120-160 characters that includes target keywords'
      });
    }

    // Image recommendations
    if (imageOptimization.missingAlt > 0) {
      recommendations.push({
        category: 'images',
        priority: 'medium',
        title: 'Add Alt Text to Images',
        description: `${imageOptimization.missingAlt} images are missing alt text`,
        impact: 'Alt text improves accessibility and helps search engines understand images',
        howToFix: 'Add descriptive alt text to all images that describes their content and context'
      });
    }

    return recommendations;
  }

  private calculateOnSiteScore(contentAnalysis: any, technicalElements: any, internalLinking: any, imageOptimization: any): number {
    let score = 0;
    let maxScore = 0;

    // Content scoring (40% of total)
    maxScore += 40;
    if (contentAnalysis.wordCount >= 300) score += 15;
    if (contentAnalysis.readabilityScore >= 60) score += 10;
    if (contentAnalysis.keywordDensity.length > 0) score += 10;
    if (contentAnalysis.headingStructure.h1.length === 1) score += 5;

    // Technical scoring (40% of total)
    maxScore += 40;
    if (technicalElements.metaTags.title.optimized) score += 15;
    if (technicalElements.metaTags.description.optimized) score += 15;
    if (technicalElements.openGraph.present) score += 5;
    if (technicalElements.structuredData.present) score += 5;

    // Images and linking (20% of total)
    maxScore += 20;
    if (imageOptimization.missingAlt === 0) score += 10;
    if (internalLinking.totalLinks > 0) score += 10;

    return Math.round((score / maxScore) * 100);
  }
}

export class GeminiService {
  private apiKey: string;
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async comprehensiveSeoOptimization(
    products: Product[],
    options: {
      updateProductTitle: boolean;
      updateProductDescription: boolean;
      updateSeoFields: boolean;
      updateHandle: boolean;
      updateImageAlts: boolean;
    }
  ): Promise<ComprehensiveSeoResult[]> {
    const results: ComprehensiveSeoResult[] = [];
    const batchSize = 10; // Optimal batch size for comprehensive analysis

    console.log(`🚀 Starting comprehensive SEO optimization for ${products.length} products`);
    console.log(`📊 Processing options:`, options);

    // Process products in batches
    for (let i = 0; i < products.length; i += batchSize) {
      const batch = products.slice(i, i + batchSize);
      console.log(`🔄 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(products.length / batchSize)} (${batch.length} products)`);

      try {
        const startTime = Date.now();
        const batchResults = await this.optimizeBatchComprehensive(batch, options);
        const processingTime = Date.now() - startTime;

        // Add processing time to results
        batchResults.forEach(result => {
          result.processingTime = processingTime / batch.length;
        });

        results.push(...batchResults);

        // Add delay between batches for rate limiting
        if (i + batchSize < products.length) {
          console.log('⏳ Waiting 3 seconds before next batch...');
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      } catch (error) {
        console.error(`❌ Error processing batch starting at index ${i}:`, error);

        // Add fallback results for failed batch
        const fallbackResults = batch.map(product => ({
          productId: product.id,
          originalProductTitle: product.title,
          originalProductDescription: product.description,
          originalSeoTitle: (product as any).seoTitle || '',
          originalSeoDescription: (product as any).seoDescription || '',
          originalHandle: (product as any).handle || '',
          optimizedProductTitle: product.title,
          optimizedProductDescription: product.description,
          optimizedSeoTitle: (product as any).seoTitle || product.title,
          optimizedSeoDescription: (product as any).seoDescription || product.description,
          optimizedHandle: (product as any).handle || '',
          targetKeywords: [],
          viralKeyword: '',
          seoScore: 0,
          competitorAnalysis: '',
          imageAltTexts: [],
          processingTime: 0,
          error: error instanceof Error ? error.message : 'Batch processing failed',
        }));

        results.push(...fallbackResults);
      }
    }

    console.log(`✅ Comprehensive SEO optimization completed for ${results.length} products`);
    return results;
  }

  private async optimizeBatchComprehensive(
    products: Product[],
    options: {
      updateProductTitle: boolean;
      updateProductDescription: boolean;
      updateSeoFields: boolean;
      updateHandle: boolean;
      updateImageAlts: boolean;
    },
    retryCount = 0
  ): Promise<ComprehensiveSeoResult[]> {
    const batchPrompt = this.createComprehensiveSeoPrompt(products, options);

    try {
      const response = await this.generateBatchContent(batchPrompt);
      return this.parseComprehensiveSeoResponse(response, products);
    } catch (error) {
      console.error(`❌ Comprehensive SEO optimization failed (attempt ${retryCount + 1}):`, error);

      // If JSON parsing failed and we have multiple products, try smaller batches
      if (error instanceof Error && error.message.includes('JSON parsing failed') && products.length > 1 && retryCount < 2) {
        console.log(`🔄 Retrying with smaller batch size: ${Math.ceil(products.length / 2)} products`);

        // Split into smaller batches
        const midpoint = Math.ceil(products.length / 2);
        const batch1 = products.slice(0, midpoint);
        const batch2 = products.slice(midpoint);

        const results1 = await this.optimizeBatchComprehensive(batch1, options, retryCount + 1);
        const results2 = await this.optimizeBatchComprehensive(batch2, options, retryCount + 1);

        return [...results1, ...results2];
      }

      throw error;
    }
  }

  private createComprehensiveSeoPrompt(
    products: Product[],
    options: {
      updateProductTitle: boolean;
      updateProductDescription: boolean;
      updateSeoFields: boolean;
      updateHandle: boolean;
      updateImageAlts: boolean;
    }
  ): string {
    const prompt = `You are an expert SEO analyst and content strategist. Perform comprehensive SEO optimization for ${products.length} products.

CRITICAL INSTRUCTIONS:
1. Analyze each product deeply to identify viral keywords and ranking opportunities
2. Research trending search terms and competitor strategies
3. Create compelling, search-optimized content that drives conversions
4. Focus on user intent and search behavior patterns

For each product, provide a JSON object with this EXACT structure:
{
  "productId": "the_product_id",
  "optimizedProductTitle": "compelling product title with viral keywords",
  "optimizedProductDescription": "detailed product description optimized for conversions",
  "optimizedSeoTitle": "search-optimized page title (max 70 chars)",
  "optimizedSeoDescription": "meta description with call-to-action (max 160 chars)",
  "optimizedHandle": "seo-friendly-url-handle",
  "targetKeywords": ["keyword1", "keyword2", "keyword3"],
  "viralKeyword": "main viral keyword to rank for",
  "seoScore": 85,
  "competitorAnalysis": "brief analysis of competitive landscape",
  "imageAltTexts": ["alt text 1", "alt text 2", "alt text 3"]
}

OPTIMIZATION REQUIREMENTS:
- Product Title: Include viral keywords, benefits, and emotional triggers
- Product Description: Detailed, benefit-focused, includes social proof elements
- SEO Title: Keyword-rich, under 70 characters, includes power words
- Meta Description: Compelling, includes CTA, under 160 characters
- URL Handle: Clean, keyword-rich, SEO-friendly
- Keywords: Mix of high-volume and long-tail keywords
- Viral Keyword: The ONE keyword with highest ranking potential
- SEO Score: Rate optimization quality (1-100)
- Competitor Analysis: Brief market positioning insights
- Image Alt Texts: Descriptive, keyword-rich alt texts for product images

Products to optimize:
${products.map((product, index) => `
${index + 1}. Product ID: ${product.id}
   Current Title: ${product.title}
   Current Description: ${product.description}
   Product Type: ${product.type}
   Vendor: ${product.vendor}
   Current SEO Title: ${(product as any).seoTitle || 'Not set'}
   Current SEO Description: ${(product as any).seoDescription || 'Not set'}
   Current Handle: ${(product as any).handle || 'Not set'}
`).join('')}

CRITICAL: Respond with ONLY a complete, valid JSON array containing exactly ${products.length} objects.
- Start with [ and end with ]
- Ensure all strings are properly quoted and escaped
- Do not truncate any responses
- Do not add any text before or after the JSON
- Each object must be complete with all required fields

JSON array:`;

    return prompt;
  }

  private async generateBatchContent(prompt: string): Promise<string> {
    const requestBody = {
      contents: [
        {
          parts: [
            {
              text: prompt
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.1, // Lower temperature for more consistent JSON
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 8192, // Significantly increased to prevent truncation
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        }
      ]
    };

    const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Gemini API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const data: GeminiResponse = await response.json();

    if (!data.candidates || data.candidates.length === 0) {
      throw new Error('No response generated from Gemini API');
    }

    const generatedText = data.candidates[0].content.parts[0].text;
    return generatedText.trim();
  }

  private parseComprehensiveSeoResponse(response: string, products: Product[]): ComprehensiveSeoResult[] {
    try {
      console.log('🔍 Parsing comprehensive SEO response...');

      // Clean the response to extract JSON
      let jsonStr = response;

      // Remove markdown code blocks if present
      jsonStr = jsonStr.replace(/```json\s*/g, '').replace(/```\s*/g, '');

      // Try to find JSON array in the response
      const jsonMatch = jsonStr.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        jsonStr = jsonMatch[0];
      }

      // Fix common JSON issues
      jsonStr = this.fixTruncatedJson(jsonStr);

      let parsedResults;
      try {
        parsedResults = JSON.parse(jsonStr);
      } catch (parseError) {
        console.error('❌ Failed to parse JSON:', parseError);
        console.error('Raw JSON string (first 500 chars):', jsonStr.substring(0, 500));
        console.error('Raw JSON string (last 500 chars):', jsonStr.substring(Math.max(0, jsonStr.length - 500)));

        // Try to fix and parse again
        const fixedJson = this.attemptJsonFix(jsonStr);
        try {
          parsedResults = JSON.parse(fixedJson);
          console.log('✅ Successfully parsed after JSON fix');
        } catch (secondError) {
          throw new Error(`JSON parsing failed: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`);
        }
      }

      if (!Array.isArray(parsedResults)) {
        throw new Error('Response is not an array');
      }

      console.log(`✅ Successfully parsed ${parsedResults.length} SEO optimization results`);

      // Map the results to our expected format
      const results: ComprehensiveSeoResult[] = products.map(product => {
        const aiResult = parsedResults.find(r => r.productId === product.id);

        if (aiResult) {
          return {
            productId: product.id,
            // Original data
            originalProductTitle: product.title,
            originalProductDescription: product.description,
            originalSeoTitle: (product as any).seoTitle || '',
            originalSeoDescription: (product as any).seoDescription || '',
            originalHandle: (product as any).handle || '',
            // Optimized content
            optimizedProductTitle: aiResult.optimizedProductTitle || product.title,
            optimizedProductDescription: aiResult.optimizedProductDescription || product.description,
            optimizedSeoTitle: aiResult.optimizedSeoTitle || product.title,
            optimizedSeoDescription: aiResult.optimizedSeoDescription || product.description,
            optimizedHandle: aiResult.optimizedHandle || (product as any).handle || '',
            // SEO analysis
            targetKeywords: aiResult.targetKeywords || [],
            viralKeyword: aiResult.viralKeyword || '',
            seoScore: aiResult.seoScore || 0,
            competitorAnalysis: aiResult.competitorAnalysis || '',
            imageAltTexts: aiResult.imageAltTexts || [],
            processingTime: 0, // Will be set by caller
          };
        } else {
          // Fallback if AI didn't provide result for this product
          return {
            productId: product.id,
            originalProductTitle: product.title,
            originalProductDescription: product.description,
            originalSeoTitle: (product as any).seoTitle || '',
            originalSeoDescription: (product as any).seoDescription || '',
            originalHandle: (product as any).handle || '',
            optimizedProductTitle: product.title,
            optimizedProductDescription: product.description,
            optimizedSeoTitle: (product as any).seoTitle || product.title,
            optimizedSeoDescription: (product as any).seoDescription || product.description,
            optimizedHandle: (product as any).handle || '',
            targetKeywords: [],
            viralKeyword: '',
            seoScore: 0,
            competitorAnalysis: '',
            imageAltTexts: [],
            processingTime: 0,
            error: 'AI did not provide optimization for this product',
          };
        }
      });

      return results;
    } catch (error) {
      console.error('❌ Failed to parse comprehensive SEO response:', error);
      console.error('Raw response:', response.substring(0, 500) + '...');

      // Return fallback results
      return products.map(product => ({
        productId: product.id,
        originalProductTitle: product.title,
        originalProductDescription: product.description,
        originalSeoTitle: (product as any).seoTitle || '',
        originalSeoDescription: (product as any).seoDescription || '',
        originalHandle: (product as any).handle || '',
        optimizedProductTitle: product.title,
        optimizedProductDescription: product.description,
        optimizedSeoTitle: (product as any).seoTitle || product.title,
        optimizedSeoDescription: (product as any).seoDescription || product.description,
        optimizedHandle: (product as any).handle || '',
        targetKeywords: [],
        viralKeyword: '',
        seoScore: 0,
        competitorAnalysis: '',
        imageAltTexts: [],
        processingTime: 0,
        error: 'Failed to parse AI response',
      }));
    }
  }





  static validateApiKey(apiKey: string): boolean {
    // Basic validation - Gemini API keys typically start with 'AIza'
    return apiKey.length > 20 && apiKey.startsWith('AIza');
  }

  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const testProduct: Product = {
        id: 'test',
        title: 'Test Product',
        description: 'This is a test product',
        type: 'Test',
        vendor: 'Test Vendor',
      };

      // Test with comprehensive SEO optimization
      const results = await this.comprehensiveSeoOptimization([testProduct], {
        updateProductTitle: true,
        updateProductDescription: true,
        updateSeoFields: true,
        updateHandle: true,
        updateImageAlts: true,
      });

      if (results.length > 0 && !results[0].error) {
        return { success: true };
      } else {
        return { success: false, error: results[0]?.error || 'Test failed' };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Connection test failed',
      };
    }
  }

  private fixTruncatedJson(jsonStr: string): string {
    let cleaned = jsonStr.trim();

    // Handle common truncation patterns

    // 1. If it doesn't end properly, find the last complete object
    if (!cleaned.endsWith(']') && !cleaned.endsWith('}')) {
      console.log('🔧 Detected truncated JSON, attempting to fix...');

      // Find the last complete closing brace
      const lastClosingBrace = cleaned.lastIndexOf('}');
      if (lastClosingBrace !== -1) {
        // Get everything up to and including the last complete object
        cleaned = cleaned.substring(0, lastClosingBrace + 1);

        // If we're in an array, ensure it closes properly
        if (cleaned.trim().startsWith('[')) {
          // Check if we need to remove a trailing comma
          if (cleaned.trim().endsWith(',')) {
            cleaned = cleaned.trim().slice(0, -1);
          }
          cleaned = cleaned + '\n]';
        }
      }
    }

    // 2. Handle incomplete strings (common issue)
    // Remove incomplete string values that don't have closing quotes
    cleaned = cleaned.replace(/:\s*"[^"]*$/, ': ""');
    cleaned = cleaned.replace(/,\s*"[^"]*$/, '');

    // 3. Handle incomplete arrays
    cleaned = cleaned.replace(/:\s*\[[^\]]*$/, ': []');

    // 4. Ensure proper array structure
    if (cleaned.startsWith('[') && !cleaned.endsWith(']')) {
      // Remove any trailing comma or incomplete content
      cleaned = cleaned.replace(/,\s*$/, '');
      cleaned = cleaned + '\n]';
    }

    return cleaned;
  }

  private attemptJsonFix(jsonStr: string): string {
    let fixed = jsonStr.trim();
    console.log('🔧 Attempting advanced JSON fix...');

    // Advanced fixes for truncated JSON

    // 1. Handle incomplete property values
    // Fix incomplete strings (most common issue)
    fixed = fixed.replace(/:\s*"[^"]*$/, ': "incomplete_value_removed"');

    // Fix incomplete numbers
    fixed = fixed.replace(/:\s*\d+\.?\d*[^\d\s,}\]]*$/, ': 0');

    // Fix incomplete arrays
    fixed = fixed.replace(/:\s*\[[^\]]*$/, ': []');

    // Fix incomplete objects
    fixed = fixed.replace(/:\s*\{[^}]*$/, ': {}');

    // 2. Handle trailing commas and incomplete properties
    fixed = fixed.replace(/,\s*"[^"]*$/, '');
    fixed = fixed.replace(/,\s*$/, '');

    // 3. Ensure proper array structure
    if (fixed.startsWith('[')) {
      // Find the last complete object
      const lastClosingBrace = fixed.lastIndexOf('}');
      if (lastClosingBrace !== -1) {
        // Keep everything up to the last complete object
        fixed = fixed.substring(0, lastClosingBrace + 1);

        // Remove trailing comma if present
        if (fixed.trim().endsWith(',')) {
          fixed = fixed.trim().slice(0, -1);
        }

        // Add closing bracket if missing
        if (!fixed.endsWith(']')) {
          fixed += '\n]';
        }
      } else {
        // No complete objects found, return empty array
        fixed = '[]';
      }
    }

    // 4. Fix escape sequences
    fixed = fixed.replace(/\n/g, '\\n');
    fixed = fixed.replace(/\r/g, '\\r');
    fixed = fixed.replace(/\t/g, '\\t');

    // 5. Final validation - ensure it's a valid JSON structure
    if (!fixed.startsWith('[') && !fixed.startsWith('{')) {
      console.log('⚠️ JSON doesn\'t start with [ or {, wrapping in array');
      fixed = '[' + fixed + ']';
    }

    console.log('✅ JSON fix completed');
    return fixed;
  }
}
