import type { ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { getTursoPrismaAdapter } from "../lib/turso-prisma-adapter.server";

const db = getTursoPrismaAdapter();
import { logError } from "../utils/error-handling.server";
import { webhookReliabilityService } from "../services/webhook-reliability.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    // Shopify automatically verifies webhook authenticity
    const { payload, session, topic, shop } = await authenticate.webhook(request);

    console.log(`📨 Received ${topic} webhook for ${shop}`);

    // Process webhook through reliability service
    const result = await webhookReliabilityService.processWebhook(
      topic,
      shop,
      payload,
      Object.fromEntries(request.headers.entries()),
      { action: 'webhook_subscription_update', shop }
    );

    if (!result.success) {
      console.error(`❌ Webhook processing failed: ${result.error}`);
      return new Response('Webhook processing failed', { status: 500 });
    }

    console.log(`✅ Webhook queued successfully: ${result.eventId}`);
    
    if (!payload?.app_subscription) {
      console.error('❌ Invalid webhook payload: missing app_subscription');
      return new Response('Invalid payload', { status: 400 });
    }

    const subscription = payload.app_subscription;
    const subscriptionId = subscription.admin_graphql_api_id || subscription.id;
    const status = subscription.status;
    const trialDays = subscription.trial_days || 0;
    const trialEndsOn = subscription.trial_ends_on;
    const planName = subscription.name || 'unknown';
    const isTest = subscription.test || false;

    console.log(`📋 Processing subscription: ${subscriptionId}, status: ${status}`);

    // Start database transaction for consistency
    await db.$transaction(async (tx) => {
      // Update session - try by session object first, then by shop
      if (session) {
        await tx.session.update({
          where: {
            id: session.id
          },
          data: {
            subscriptionId: subscriptionId,
            subscriptionStatus: status,
            billingPlanId: determinePlanIdFromSubscription(subscription),
            trialEndsAt: trialEndsOn ? new Date(trialEndsOn) : null,
            lastBillingCheck: new Date(),
          },
        });
        console.log(`✅ Updated session for shop: ${shop}`);
      } else {
        // Fallback: update by shop if session object not available
        await tx.session.updateMany({
          where: { shop: shop },
          data: {
            subscriptionId: subscriptionId,
            subscriptionStatus: status,
            billingPlanId: determinePlanIdFromSubscription(subscription),
            trialEndsAt: trialEndsOn ? new Date(trialEndsOn) : null,
            lastBillingCheck: new Date(),
          },
        });
        console.log(`✅ Updated session by shop: ${shop}`);
      }

      // Update or create billing subscription record
      await tx.billingSubscription.upsert({
        where: {
          subscriptionId: subscriptionId
        },
        update: {
          status: status,
          trialDays: trialDays,
          trialEndsAt: trialEndsOn ? new Date(trialEndsOn) : null,
          currentPeriodStart: subscription.created_at ? new Date(subscription.created_at) : null,
          currentPeriodEnd: subscription.current_period_end ? new Date(subscription.current_period_end) : null,
          priceAmount: subscription.line_items?.[0]?.price ? parseFloat(subscription.line_items[0].price) : null,
          updatedAt: new Date()
        },
        create: {
          shop,
          subscriptionId: subscriptionId,
          planId: determinePlanIdFromSubscription(subscription),
          status: status,
          trialDays: trialDays,
          trialEndsAt: trialEndsOn ? new Date(trialEndsOn) : null,
          currentPeriodStart: subscription.created_at ? new Date(subscription.created_at) : new Date(),
          currentPeriodEnd: subscription.current_period_end ? new Date(subscription.current_period_end) : null,
          priceAmount: subscription.line_items?.[0]?.price ? parseFloat(subscription.line_items[0].price) : null,
          priceCurrency: 'USD'
        }
      });

      // Log billing event
      await tx.billingEvent.create({
        data: {
          shop,
          eventType: 'subscription_updated',
          referenceId: subscriptionId,
          eventData: JSON.stringify({
            status: status,
            trialDays: trialDays,
            trialEndsOn: trialEndsOn,
            planName: planName,
            isTest: isTest,
            webhookTopic: topic,
            timestamp: new Date().toISOString()
          })
        }
      });

      console.log(`✅ Processed subscription update for shop: ${shop}, status: ${status}`);
    });

    // Invalidate billing cache after database update
    const { invalidateBillingCache } = await import("../utils/cache.server");
    invalidateBillingCache(shop, 'subscription');
    console.log(`🔄 Billing cache invalidated for shop: ${shop} after subscription update`);

    // Handle specific status changes
    switch (subscription.status) {
      case 'ACTIVE':
        console.log(`Subscription ${subscriptionId} is now active for shop ${shop}`);
        break;
      case 'CANCELLED':
        console.log(`Subscription ${subscriptionId} was cancelled for shop ${shop}`);
        // Additional cleanup for cancelled subscriptions
        try {
          await db.session.updateMany({
            where: { shop: shop },
            data: {
              subscriptionId: null,
              subscriptionStatus: 'CANCELLED',
              billingPlanId: 'pay_per_use',
              lastBillingCheck: new Date()
            }
          });
          console.log(`✅ Session updated for cancelled subscription: ${subscriptionId}`);
        } catch (error) {
          console.error(`⚠️ Failed to update session for cancelled subscription:`, error);
        }
        break;
      case 'EXPIRED':
        console.log(`Subscription ${subscriptionId} has expired for shop ${shop}`);
        break;
      case 'PENDING':
        if (subscription.trial_days && subscription.trial_days > 0) {
          console.log(`Subscription ${subscriptionId} is in trial period (${subscription.trial_days} days) for shop ${shop}`);
        } else {
          console.log(`Subscription ${subscriptionId} is pending payment for shop ${shop}`);
        }
        break;
    }
    
    console.log(`✅ Webhook processed successfully`);
    return new Response("OK", { status: 200 });
  } catch (error) {
    console.error('❌ App subscription webhook error:', error);

    // Log the error for debugging
    await logError(
      error instanceof Error ? error : new Error('Webhook processing failed'),
      {
        action: 'webhook_subscription_update'
      }
    );

    // Return appropriate error response
    if (error instanceof Error && error.message.includes('RATE_LIMIT_EXCEEDED')) {
      return new Response('Rate limit exceeded', { status: 429 });
    }

    return new Response("Error processing webhook", { status: 500 });
  }
};

function determinePlanIdFromSubscription(subscription: any): string {
  // This would need to be implemented based on your subscription structure
  // For now, return a default value
  const lineItem = subscription.line_items?.[0];
  if (!lineItem) return 'unknown';
  
  // You might determine this based on price, name, or other attributes
  const price = parseFloat(lineItem.price || '0');
  
  if (price === 199.99) return 'annual';
  if (price === 19.99) return 'monthly';
  
  return 'unknown';
}
