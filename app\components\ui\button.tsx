import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-bold transition-all duration-300 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-offset-2 focus-visible:ring-offset-black relative overflow-hidden group active:scale-95",
  {
    variants: {
      variant: {
        default:
          "bg-white text-black hover:bg-gray-100 shadow-lg hover:shadow-2xl border border-gray-200 hover:border-gray-300 transform hover:scale-[1.02]",
        destructive:
          "bg-black text-white hover:bg-gray-900 shadow-lg hover:shadow-2xl border border-gray-800 hover:border-gray-700 transform hover:scale-[1.02]",
        outline:
          "border-2 border-gray-300 bg-transparent text-black hover:bg-black hover:text-white hover:border-black shadow-md hover:shadow-xl transform hover:scale-[1.02]",
        secondary:
          "bg-gray-100 text-black hover:bg-gray-200 shadow-md hover:shadow-lg border border-gray-200 hover:border-gray-300 transform hover:scale-[1.02]",
        ghost:
          "text-black hover:bg-gray-100 rounded-full transform hover:scale-[1.02] hover:shadow-md",
        link: "text-black underline-offset-4 hover:underline hover:text-gray-700 transition-colors",
      },
      size: {
        default: "h-12 px-8 py-3 text-sm",
        sm: "h-10 px-6 py-2 text-xs",
        lg: "h-14 px-10 py-4 text-base",
        icon: "size-12",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean
  }) {
  const Comp = asChild ? Slot : "button"

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  )
}

export { Button, buttonVariants }
