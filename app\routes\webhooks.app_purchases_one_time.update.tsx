import type { ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { getTursoPrismaAdapter } from "../lib/turso-prisma-adapter.server";

const db = getTursoPrismaAdapter();

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const { payload, session, topic, shop } = await authenticate.webhook(request);
    
    console.log(`Received ${topic} webhook for ${shop}:`, JSON.stringify(payload, null, 2));
    
    if (!payload?.app_purchase_one_time) {
      console.error('❌ Invalid webhook payload: missing app_purchase_one_time');
      return new Response('Invalid payload', { status: 400 });
    }

    const purchase = payload.app_purchase_one_time;
    const purchaseId = purchase.admin_graphql_api_id || purchase.id;
    const status = purchase.status;
    const priceAmount = purchase.price?.amount ? parseFloat(purchase.price.amount) : 0;
    const currency = purchase.price?.currency_code || 'USD';
    const productCount = Math.round(priceAmount / 0.10); // Each product costs $0.10
    const description = purchase.name || 'One-time purchase';

    console.log(`📋 Processing purchase: ${purchaseId}, status: ${status}, amount: ${priceAmount}, products: ${productCount}`);

    // Start database transaction for consistency
    await db.$transaction(async (tx) => {
      // Update session if it exists
      if (session) {
        await tx.session.update({
          where: {
            id: session.id
          },
          data: {
            lastBillingCheck: new Date(),
          },
        });
        console.log(`✅ Updated session for shop: ${shop}`);
      }

      // Update or create billing purchase record
      await tx.billingPurchase.upsert({
        where: {
          purchaseId: purchaseId
        },
        update: {
          status: status,
          updatedAt: new Date()
        },
        create: {
          shop,
          purchaseId: purchaseId,
          status: status,
          amount: priceAmount,
          currency: currency,
          productCount: productCount,
          description: description
        }
      });

      // Log billing event
      await tx.billingEvent.create({
        data: {
          shop,
          eventType: 'purchase_updated',
          referenceId: purchaseId,
          eventData: JSON.stringify({
            status: status,
            amount: priceAmount,
            currency: currency,
            productCount: productCount,
            description: description,
            webhookTopic: topic,
            timestamp: new Date().toISOString()
          })
        }
      });

      console.log(`✅ Processed purchase update for shop: ${shop}, status: ${status}`);
    });

    // Handle specific status changes
    switch (purchase.status) {
      case 'ACCEPTED':
        console.log(`✅ One-time purchase ${purchaseId} was accepted for shop ${shop} - ${productCount} credits available`);
        break;
      case 'DECLINED':
        console.log(`❌ One-time purchase ${purchaseId} was declined for shop ${shop}`);
        break;
      case 'PENDING':
        console.log(`⏳ One-time purchase ${purchaseId} is pending for shop ${shop}`);
        break;
    }
    
    return new Response("OK", { status: 200 });
  } catch (error) {
    console.error('App purchase one-time webhook error:', error);
    return new Response("Error processing webhook", { status: 500 });
  }
};


