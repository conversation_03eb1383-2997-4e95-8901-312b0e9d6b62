/**
 * Enhanced Error Handling Utility
 * Provides comprehensive error handling, logging, and user feedback
 */

import { getTursoPrismaAdapter } from "../lib/turso-prisma-adapter.server";

const db = getTursoPrismaAdapter();

export interface ErrorContext {
  shop?: string;
  userId?: string;
  action?: string;
  metadata?: Record<string, any>;
}

export interface AppError {
  code: string;
  message: string;
  userMessage: string;
  statusCode: number;
  context?: ErrorContext;
  originalError?: Error;
}

/**
 * Enhanced error class with context and user-friendly messages
 */
export class EnhancedError extends Error {
  public readonly code: string;
  public readonly userMessage: string;
  public readonly statusCode: number;
  public readonly context?: ErrorContext;
  public readonly originalError?: Error;

  constructor(appError: AppError) {
    super(appError.message);
    this.name = 'EnhancedError';
    this.code = appError.code;
    this.userMessage = appError.userMessage;
    this.statusCode = appError.statusCode;
    this.context = appError.context;
    this.originalError = appError.originalError;
  }
}

/**
 * Error codes and their corresponding user messages
 */
export const ERROR_CODES = {
  // Authentication Errors
  AUTH_SESSION_EXPIRED: {
    code: 'AUTH_SESSION_EXPIRED',
    message: 'Session has expired',
    userMessage: 'Your session has expired. Please log in again.',
    statusCode: 401
  },
  AUTH_INVALID_SESSION: {
    code: 'AUTH_INVALID_SESSION',
    message: 'Invalid session',
    userMessage: 'Invalid session. Please log in again.',
    statusCode: 401
  },
  AUTH_MISSING_PERMISSIONS: {
    code: 'AUTH_MISSING_PERMISSIONS',
    message: 'Missing required permissions',
    userMessage: 'You don\'t have permission to perform this action.',
    statusCode: 403
  },
  AUTH_ACCOUNT_LOCKED: {
    code: 'AUTH_ACCOUNT_LOCKED',
    message: 'Account temporarily locked',
    userMessage: 'Account is temporarily locked due to too many failed attempts. Please try again later.',
    statusCode: 423
  },
  ROUTE_ACCESS_DENIED: {
    code: 'ROUTE_ACCESS_DENIED',
    message: 'Route access denied',
    userMessage: 'You don\'t have permission to access this page.',
    statusCode: 403
  },

  // Billing Errors
  BILLING_SUBSCRIPTION_NOT_FOUND: {
    code: 'BILLING_SUBSCRIPTION_NOT_FOUND',
    message: 'Subscription not found',
    userMessage: 'No active subscription found. Please subscribe to continue.',
    statusCode: 404
  },
  BILLING_PAYMENT_FAILED: {
    code: 'BILLING_PAYMENT_FAILED',
    message: 'Payment processing failed',
    userMessage: 'Payment failed. Please check your payment method and try again.',
    statusCode: 402
  },
  BILLING_INSUFFICIENT_CREDITS: {
    code: 'BILLING_INSUFFICIENT_CREDITS',
    message: 'Insufficient credits',
    userMessage: 'You don\'t have enough credits. Please purchase more to continue.',
    statusCode: 402
  },
  BILLING_TRIAL_EXPIRED: {
    code: 'BILLING_TRIAL_EXPIRED',
    message: 'Trial period has expired',
    userMessage: 'Your trial period has expired. Please subscribe to continue using the app.',
    statusCode: 402
  },

  // Validation Errors
  VALIDATION_INVALID_INPUT: {
    code: 'VALIDATION_INVALID_INPUT',
    message: 'Invalid input provided',
    userMessage: 'Please check your input and try again.',
    statusCode: 400
  },
  VALIDATION_MISSING_REQUIRED_FIELD: {
    code: 'VALIDATION_MISSING_REQUIRED_FIELD',
    message: 'Required field is missing',
    userMessage: 'Please fill in all required fields.',
    statusCode: 400
  },
  VALIDATION_CSRF_FAILED: {
    code: 'VALIDATION_CSRF_FAILED',
    message: 'CSRF validation failed',
    userMessage: 'Security validation failed. Please refresh the page and try again.',
    statusCode: 403
  },

  // API Errors
  API_RATE_LIMIT_EXCEEDED: {
    code: 'API_RATE_LIMIT_EXCEEDED',
    message: 'API rate limit exceeded',
    userMessage: 'Too many requests. Please wait a moment and try again.',
    statusCode: 429
  },
  RATE_LIMIT_EXCEEDED: {
    code: 'RATE_LIMIT_EXCEEDED',
    message: 'Rate limit exceeded',
    userMessage: 'Too many requests. Please wait a moment and try again.',
    statusCode: 429
  },
  API_SHOPIFY_ERROR: {
    code: 'API_SHOPIFY_ERROR',
    message: 'Shopify API error',
    userMessage: 'There was an issue connecting to Shopify. Please try again.',
    statusCode: 502
  },
  API_EXTERNAL_SERVICE_ERROR: {
    code: 'API_EXTERNAL_SERVICE_ERROR',
    message: 'External service error',
    userMessage: 'External service is temporarily unavailable. Please try again later.',
    statusCode: 503
  },
  GRAPHQL_QUERY_TOO_COMPLEX: {
    code: 'GRAPHQL_QUERY_TOO_COMPLEX',
    message: 'GraphQL query too complex',
    userMessage: 'The request is too complex. Please try with fewer items.',
    statusCode: 400
  },

  // Database Errors
  DATABASE_CONNECTION_ERROR: {
    code: 'DATABASE_CONNECTION_ERROR',
    message: 'Database connection failed',
    userMessage: 'Database is temporarily unavailable. Please try again.',
    statusCode: 503
  },
  DATABASE_CONSTRAINT_VIOLATION: {
    code: 'DATABASE_CONSTRAINT_VIOLATION',
    message: 'Database constraint violation',
    userMessage: 'Data validation failed. Please check your input.',
    statusCode: 400
  },

  // SEO Optimization Errors
  SEO_OPTIMIZATION_IN_PROGRESS: {
    code: 'SEO_OPTIMIZATION_IN_PROGRESS',
    message: 'SEO optimization already in progress',
    userMessage: 'An SEO optimization is already running. Please wait for it to complete.',
    statusCode: 409
  },

  // Generic Errors
  INTERNAL_SERVER_ERROR: {
    code: 'INTERNAL_SERVER_ERROR',
    message: 'Internal server error',
    userMessage: 'Something went wrong. Please try again or contact support.',
    statusCode: 500
  },
  NOT_FOUND: {
    code: 'NOT_FOUND',
    message: 'Resource not found',
    userMessage: 'The requested resource was not found.',
    statusCode: 404
  }
} as const;

/**
 * Create an enhanced error with context
 */
export function createError(
  errorCode: keyof typeof ERROR_CODES,
  context?: ErrorContext,
  originalError?: Error
): EnhancedError {
  const errorDef = ERROR_CODES[errorCode];
  return new EnhancedError({
    ...errorDef,
    context,
    originalError
  });
}

/**
 * Log error to database and console
 */
export async function logError(error: EnhancedError | Error, context?: ErrorContext): Promise<void> {
  try {
    const errorData = error instanceof EnhancedError ? error : {
      code: 'UNKNOWN_ERROR',
      message: error.message,
      userMessage: 'An unexpected error occurred',
      statusCode: 500,
      context,
      originalError: error
    };

    // Log to console with context
    console.error('❌ Error occurred:', {
      code: errorData.code,
      message: errorData.message,
      context: errorData.context,
      stack: error.stack
    });

    // Log to database if shop context is available
    if (errorData.context?.shop) {
      await db.billingEvent.create({
        data: {
          shop: errorData.context.shop,
          eventType: 'error_logged',
          referenceId: null,
          eventData: JSON.stringify({
            errorCode: errorData.code,
            errorMessage: errorData.message,
            userMessage: errorData.userMessage,
            statusCode: errorData.statusCode,
            context: errorData.context,
            stack: error.stack?.substring(0, 1000), // Limit stack trace length
            timestamp: new Date().toISOString()
          })
        }
      });
    }
  } catch (logError) {
    console.error('❌ Failed to log error:', logError);
  }
}

/**
 * Handle errors in route loaders and actions
 */
export async function handleRouteError(
  error: unknown,
  context?: ErrorContext
): Promise<Response> {
  let enhancedError: EnhancedError;

  if (error instanceof EnhancedError) {
    enhancedError = error;
  } else if (error instanceof Error) {
    // Try to categorize the error
    if (error.message.includes('CSRF')) {
      enhancedError = createError('VALIDATION_CSRF_FAILED', context, error);
    } else if (error.message.includes('session')) {
      enhancedError = createError('AUTH_INVALID_SESSION', context, error);
    } else if (error.message.includes('rate limit')) {
      enhancedError = createError('API_RATE_LIMIT_EXCEEDED', context, error);
    } else if (error.message.includes('database') || error.message.includes('prisma')) {
      enhancedError = createError('DATABASE_CONNECTION_ERROR', context, error);
    } else {
      enhancedError = createError('INTERNAL_SERVER_ERROR', context, error);
    }
  } else {
    enhancedError = createError('INTERNAL_SERVER_ERROR', context);
  }

  // Log the error
  await logError(enhancedError, context);

  // Return appropriate response
  return new Response(
    JSON.stringify({
      error: {
        code: enhancedError.code,
        message: enhancedError.userMessage,
        statusCode: enhancedError.statusCode
      }
    }),
    {
      status: enhancedError.statusCode,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * Validate required fields in form data
 */
export function validateRequiredFields(
  formData: FormData,
  requiredFields: string[],
  context?: ErrorContext
): void {
  const missingFields = requiredFields.filter(field => !formData.get(field));
  
  if (missingFields.length > 0) {
    throw createError('VALIDATION_MISSING_REQUIRED_FIELD', {
      ...context,
      metadata: { missingFields }
    });
  }
}

/**
 * Validate email format
 */
export function validateEmail(email: string, context?: ErrorContext): void {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw createError('VALIDATION_INVALID_INPUT', {
      ...context,
      metadata: { field: 'email', value: email }
    });
  }
}

/**
 * Validate positive number
 */
export function validatePositiveNumber(value: string | number, fieldName: string, context?: ErrorContext): number {
  const num = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(num) || num <= 0) {
    throw createError('VALIDATION_INVALID_INPUT', {
      ...context,
      metadata: { field: fieldName, value }
    });
  }
  
  return num;
}

/**
 * Wrap async functions with error handling
 */
export function withErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  context?: ErrorContext
) {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      if (error instanceof EnhancedError) {
        throw error;
      }
      
      const enhancedError = error instanceof Error 
        ? createError('INTERNAL_SERVER_ERROR', context, error)
        : createError('INTERNAL_SERVER_ERROR', context);
      
      await logError(enhancedError, context);
      throw enhancedError;
    }
  };
}
