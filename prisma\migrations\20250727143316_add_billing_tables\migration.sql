/*
  Warnings:

  - A unique constraint covering the columns `[shop]` on the table `Session` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Session" ADD COLUMN "billingPlanId" TEXT;
ALTER TABLE "Session" ADD COLUMN "lastBillingCheck" DATETIME;
ALTER TABLE "Session" ADD COLUMN "subscriptionId" TEXT;
ALTER TABLE "Session" ADD COLUMN "subscriptionStatus" TEXT;
ALTER TABLE "Session" ADD COLUMN "trialEndsAt" DATETIME;

-- CreateTable
CREATE TABLE "BillingSubscription" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "shop" TEXT NOT NULL,
    "subscriptionId" TEXT NOT NULL,
    "planId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "trialDays" INTEGER NOT NULL DEFAULT 0,
    "trialEndsAt" DATETIME,
    "currentPeriodStart" DATETIME,
    "currentPeriodEnd" DATETIME,
    "priceAmount" REAL,
    "priceCurrency" TEXT NOT NULL DEFAULT 'USD',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "BillingSubscription_shop_fkey" FOREIGN KEY ("shop") REFERENCES "Session" ("shop") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "BillingPurchase" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "shop" TEXT NOT NULL,
    "purchaseId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "productCount" INTEGER NOT NULL,
    "amount" REAL NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "description" TEXT,
    "optimizationBatchId" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "BillingPurchase_shop_fkey" FOREIGN KEY ("shop") REFERENCES "Session" ("shop") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "BillingUsage" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "shop" TEXT NOT NULL,
    "billingType" TEXT NOT NULL,
    "billingReferenceId" TEXT,
    "productsOptimized" INTEGER NOT NULL,
    "optimizationDate" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "batchId" TEXT,
    CONSTRAINT "BillingUsage_shop_fkey" FOREIGN KEY ("shop") REFERENCES "Session" ("shop") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "BillingEvent" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "shop" TEXT NOT NULL,
    "eventType" TEXT NOT NULL,
    "referenceId" TEXT,
    "eventData" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "BillingEvent_shop_fkey" FOREIGN KEY ("shop") REFERENCES "Session" ("shop") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "BillingSubscription_subscriptionId_key" ON "BillingSubscription"("subscriptionId");

-- CreateIndex
CREATE INDEX "BillingSubscription_shop_idx" ON "BillingSubscription"("shop");

-- CreateIndex
CREATE INDEX "BillingSubscription_status_idx" ON "BillingSubscription"("status");

-- CreateIndex
CREATE UNIQUE INDEX "BillingPurchase_purchaseId_key" ON "BillingPurchase"("purchaseId");

-- CreateIndex
CREATE INDEX "BillingPurchase_shop_idx" ON "BillingPurchase"("shop");

-- CreateIndex
CREATE INDEX "BillingPurchase_status_idx" ON "BillingPurchase"("status");

-- CreateIndex
CREATE INDEX "BillingUsage_shop_idx" ON "BillingUsage"("shop");

-- CreateIndex
CREATE INDEX "BillingUsage_optimizationDate_idx" ON "BillingUsage"("optimizationDate");

-- CreateIndex
CREATE INDEX "BillingEvent_shop_idx" ON "BillingEvent"("shop");

-- CreateIndex
CREATE INDEX "BillingEvent_eventType_idx" ON "BillingEvent"("eventType");

-- CreateIndex
CREATE UNIQUE INDEX "Session_shop_key" ON "Session"("shop");
