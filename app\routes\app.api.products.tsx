import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";

// Helper function to calculate basic SEO score
function calculateSeoScore(product: any): number {
  let score = 0;

  // Title optimization (30 points)
  if (product.title) {
    score += Math.min(30, product.title.length > 10 ? 30 : 15);
  }

  // Description optimization (25 points)
  if (product.description && product.description.length > 50) {
    score += 25;
  } else if (product.description) {
    score += 10;
  }

  // SEO title (25 points)
  if (product.seo?.title) {
    score += product.seo.title.length <= 70 ? 25 : 15;
  }

  // SEO description (20 points)
  if (product.seo?.description) {
    score += product.seo.description.length <= 160 ? 20 : 10;
  }

  return Math.min(100, score);
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin } = await authenticate.admin(request);
  const url = new URL(request.url);

  // Get pagination and filtering parameters
  const page = parseInt(url.searchParams.get('page') || '1');
  const search = url.searchParams.get('search') || '';

  try {
    // Fetch ALL products using cursor-based pagination to avoid query cost limits
    const allProducts = [];
    let hasNextPage = true;
    let cursor = null;
    let batchCount = 0;

    console.log('📦 Fetching all products using pagination...');

    while (hasNextPage && batchCount < 20) { // Safety limit of 20 batches (50 * 20 = 1000 products max)
      batchCount++;
      console.log(`📦 Fetching batch ${batchCount}...`);

      const response: any = await admin.graphql(`
        query getProductsWithSeo($first: Int!, $after: String, $query: String) {
          products(first: $first, after: $after, query: $query) {
            edges {
              node {
                id
                title
                description
                productType
                vendor
                handle
                seo {
                  title
                  description
                }
                createdAt
                updatedAt
              }
              cursor
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      `, {
        variables: {
          first: 50, // Smaller batch size to keep query cost low
          after: cursor,
          query: search ? `title:*${search}* OR description:*${search}*` : undefined,
        },
      });

      const responseJson: any = await response.json();
      const batchProducts = responseJson.data?.products?.edges?.map((edge: any) => {
        const product = edge.node;
        return {
          id: product.id.replace("gid://shopify/Product/", ""),
          title: product.title,
          description: product.description || "",
          type: product.productType || "Uncategorized",
          vendor: product.vendor || "Unknown",
          handle: product.handle,
          seoTitle: product.seo?.title || "",
          seoDescription: product.seo?.description || "",
          seoScore: calculateSeoScore(product),
          status: 'pending',
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
        };
      }) || [];

      allProducts.push(...batchProducts);

      hasNextPage = responseJson.data?.products?.pageInfo?.hasNextPage || false;
      cursor = responseJson.data?.products?.pageInfo?.endCursor;

      // Small delay between requests to be respectful
      if (hasNextPage) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    console.log(`✅ Successfully fetched ${allProducts.length} products in ${batchCount} batches`);
    const products = allProducts;

    // Get unique product types for filtering
    const productTypes = [...new Set(products.map((p: any) => p.type))].filter(Boolean) as string[];

    return json({
      products,
      productTypes,
      pagination: {
        currentPage: page,
        hasNextPage: false, // We fetched all available products
        hasPreviousPage: false,
      },
      totalProducts: products.length,
    });
  } catch (error) {
    console.error('Error loading products:', error);
    return json({
      products: [],
      productTypes: [],
      pagination: { currentPage: 1, hasNextPage: false, hasPreviousPage: false },
      totalProducts: 0,
      error: 'Failed to load products'
    });
  }
};
