#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Comprehensive Database Pattern Detection Script
    Scans ALL files in the project to detect any remaining usage of old database patterns

.DESCRIPTION
    This script performs an exhaustive search across the entire codebase to find:
    - Direct imports of db.server
    - Usage of old database client patterns
    - Any references that might have been missed in manual migration
#>

param(
    [string]$ProjectRoot = ".",
    [switch]$Verbose = $false
)

Write-Host "🔍 COMPREHENSIVE DATABASE PATTERN DETECTION" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host ""

# Define all patterns to search for
$patterns = @(
    @{
        Name = "Direct db.server imports"
        Pattern = 'import\s+db\s+from\s+["'']\.\.?\/.*db\.server["'']'
        Description = "Files importing db directly from db.server"
    },
    @{
        Name = "Alternative db.server imports"
        Pattern = 'from\s+["'']\.\.?\/.*db\.server["'']'
        Description = "Files importing from db.server with different syntax"
    },
    @{
        Name = "Require db.server"
        Pattern = 'require\s*\(\s*["'']\.\.?\/.*db\.server["'']'
        Description = "Files using require() to import db.server"
    },
    @{
        Name = "Direct db usage without adapter"
        Pattern = '\bdb\.(session|billingSubscription|billingPurchase|billingUsage|billingEvent|cSRFToken|rateLimitEntry|cacheEntry)\.'
        Description = "Direct usage of db.tableName without Turso adapter"
    },
    @{
        Name = "Prisma client direct usage"
        Pattern = 'new\s+PrismaClient\s*\('
        Description = "Direct instantiation of PrismaClient (should use adapter)"
    },
    @{
        Name = "Old database variable names"
        Pattern = '\bprisma\.(session|billingSubscription|billingPurchase|billingUsage|billingEvent|cSRFToken|rateLimitEntry|cacheEntry)\.'
        Description = "Usage of prisma.tableName instead of Turso adapter"
    },
    @{
        Name = "Database file references"
        Pattern = '\.db["'']|database\.sqlite|prisma\.db'
        Description = "References to database files"
    },
    @{
        Name = "Old import patterns"
        Pattern = 'import.*\{.*db.*\}.*from.*["'']\.\.?\/.*db'
        Description = "Destructured imports from db modules"
    }
)

# File extensions to scan
$extensions = @("*.ts", "*.tsx", "*.js", "*.jsx", "*.json", "*.md", "*.txt", "*.env*")

# Directories to exclude
$excludeDirs = @("node_modules", ".git", "dist", "build", ".next", "coverage", ".turbo")

Write-Host "📁 Scanning directories..." -ForegroundColor Yellow
Write-Host "Project Root: $ProjectRoot" -ForegroundColor Gray
Write-Host "Extensions: $($extensions -join ', ')" -ForegroundColor Gray
Write-Host "Excluded: $($excludeDirs -join ', ')" -ForegroundColor Gray
Write-Host ""

# Get all files to scan
$allFiles = @()
foreach ($ext in $extensions) {
    $files = Get-ChildItem -Path $ProjectRoot -Recurse -Include $ext -File | Where-Object {
        $file = $_
        $shouldExclude = $false
        foreach ($excludeDir in $excludeDirs) {
            if ($file.FullName -like "*\$excludeDir\*" -or $file.FullName -like "*/$excludeDir/*") {
                $shouldExclude = $true
                break
            }
        }
        -not $shouldExclude
    }
    $allFiles += $files
}

Write-Host "📊 Found $($allFiles.Count) files to scan" -ForegroundColor Green
Write-Host ""

# Results tracking
$totalIssues = 0
$issuesByFile = @{}
$issuesByPattern = @{}

# Scan each file for each pattern
foreach ($pattern in $patterns) {
    Write-Host "🔍 Searching for: $($pattern.Name)" -ForegroundColor Cyan
    Write-Host "   Pattern: $($pattern.Pattern)" -ForegroundColor Gray
    
    $patternMatches = 0
    
    foreach ($file in $allFiles) {
        try {
            $content = Get-Content -Path $file.FullName -Raw -ErrorAction SilentlyContinue
            if ($content) {
                $matches = [regex]::Matches($content, $pattern.Pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase -bor [System.Text.RegularExpressions.RegexOptions]::Multiline)
                
                if ($matches.Count -gt 0) {
                    $patternMatches += $matches.Count
                    $totalIssues += $matches.Count
                    
                    $relativePath = $file.FullName.Replace($PWD.Path, "").TrimStart('\', '/')
                    
                    if (-not $issuesByFile.ContainsKey($relativePath)) {
                        $issuesByFile[$relativePath] = @()
                    }
                    
                    foreach ($match in $matches) {
                        # Find line number
                        $lineNumber = ($content.Substring(0, $match.Index) -split "`n").Count
                        
                        $issue = @{
                            Pattern = $pattern.Name
                            Match = $match.Value.Trim()
                            LineNumber = $lineNumber
                            Description = $pattern.Description
                        }
                        
                        $issuesByFile[$relativePath] += $issue
                        
                        if ($Verbose) {
                            Write-Host "   ❌ $relativePath:$lineNumber - $($match.Value.Trim())" -ForegroundColor Red
                        }
                    }
                }
            }
        }
        catch {
            if ($Verbose) {
                Write-Host "   ⚠️ Could not read file: $($file.FullName)" -ForegroundColor Yellow
            }
        }
    }
    
    if ($patternMatches -gt 0) {
        Write-Host "   ❌ Found $patternMatches matches" -ForegroundColor Red
        $issuesByPattern[$pattern.Name] = $patternMatches
    } else {
        Write-Host "   ✅ No matches found" -ForegroundColor Green
    }
    Write-Host ""
}

# Generate comprehensive report
Write-Host "📋 COMPREHENSIVE SCAN RESULTS" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan
Write-Host ""

if ($totalIssues -eq 0) {
    Write-Host "🎉 PERFECT! NO OLD DATABASE PATTERNS FOUND!" -ForegroundColor Green
    Write-Host "✅ Your codebase is 100% migrated to Turso!" -ForegroundColor Green
    Write-Host "✅ All files are using the Turso adapter correctly!" -ForegroundColor Green
    Write-Host "✅ Ready for production deployment!" -ForegroundColor Green
} else {
    Write-Host "❌ FOUND $totalIssues ISSUES THAT NEED FIXING!" -ForegroundColor Red
    Write-Host ""
    
    Write-Host "📊 Issues by Pattern:" -ForegroundColor Yellow
    foreach ($pattern in $issuesByPattern.Keys) {
        Write-Host "   • $pattern`: $($issuesByPattern[$pattern]) matches" -ForegroundColor Red
    }
    Write-Host ""
    
    Write-Host "📁 Issues by File:" -ForegroundColor Yellow
    foreach ($file in $issuesByFile.Keys | Sort-Object) {
        Write-Host "   📄 $file" -ForegroundColor Red
        foreach ($issue in $issuesByFile[$file]) {
            Write-Host "      Line $($issue.LineNumber): $($issue.Match)" -ForegroundColor Gray
            Write-Host "      Pattern: $($issue.Pattern)" -ForegroundColor Gray
            Write-Host "      Description: $($issue.Description)" -ForegroundColor Gray
            Write-Host ""
        }
    }
    
    Write-Host "🔧 RECOMMENDED ACTIONS:" -ForegroundColor Yellow
    Write-Host "1. Replace all import db from with import getTursoPrismaAdapter" -ForegroundColor White
    Write-Host "2. Add const db = getTursoPrismaAdapter() after the import" -ForegroundColor White
    Write-Host "3. Ensure all database operations go through the Turso adapter" -ForegroundColor White
    Write-Host "4. Remove any direct PrismaClient instantiations" -ForegroundColor White
    Write-Host "5. Run this script again to verify all issues are resolved" -ForegroundColor White
}

Write-Host ""
Write-Host "🔍 Scan completed at $(Get-Date)" -ForegroundColor Cyan
Write-Host "📊 Total files scanned: $($allFiles.Count)" -ForegroundColor Cyan
Write-Host "📊 Total issues found: $totalIssues" -ForegroundColor Cyan

# Exit with appropriate code
if ($totalIssues -gt 0) {
    exit 1
} else {
    exit 0
}
