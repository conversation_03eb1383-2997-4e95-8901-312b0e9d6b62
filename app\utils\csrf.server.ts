/**
 * Unified CSRF Protection Service
 * Provides comprehensive Cross-Site Request Forgery protection with database storage
 */

import { createHash, randomBytes } from "crypto";
import { getTursoPrismaAdapter } from "../lib/turso-prisma-adapter.server";

const db = getTursoPrismaAdapter();
import { createError, type ErrorContext } from "./error-handling.server";
import { applyRateLimit, RATE_LIMITERS } from "./rate-limiting.server";

export interface CSRFTokenData {
  token: string;
  timestamp: number;
  shop: string;
  nonce: string;
}

export interface CSRFValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * Unified CSRF Protection Service
 */
export class CSRFService {
  private static instance: CSRFService;
  private inMemoryTokens = new Map<string, { shop: string; expires: number }>();

  static getInstance(): CSRFService {
    if (!CSRFService.instance) {
      CSRFService.instance = new CSRFService();
    }
    return CSRFService.instance;
  }

  /**
   * Generate a secure CSRF token for a specific shop
   */
  async generateToken(shop: string, context?: ErrorContext): Promise<string> {
    try {
      // Apply rate limiting for token generation
      await applyRateLimit(RATE_LIMITERS.CSRF_GENERATION(shop), context);

      const timestamp = Date.now();
      const nonce = randomBytes(16).toString('hex');
      const randomData = randomBytes(32).toString('hex');
      const secret = process.env.SESSION_SECRET || 'fallback-secret';

      // Create token data
      const tokenData: CSRFTokenData = {
        token: randomData,
        timestamp,
        shop,
        nonce
      };

      // Create signature
      const payload = JSON.stringify(tokenData);
      const signature = createHash('sha256')
        .update(payload + secret + nonce)
        .digest('hex');

      // Combine payload and signature
      const csrfToken = Buffer.from(payload).toString('base64') + '.' + signature;

      // Store token with expiration (1 hour)
      const expires = new Date(timestamp + 60 * 60 * 1000);

      try {
        // Try to store in database for persistence
        await db.safeQuery(async () => {
          await db.cSRFToken.create({
            data: {
              token: csrfToken,
              shop,
              expires
            }
          });
        });
      } catch (dbError) {
        console.warn('⚠️ Failed to store CSRF token in database, using memory fallback');
        // Fallback to in-memory storage
        this.inMemoryTokens.set(csrfToken, { shop, expires: expires.getTime() });
      }

      console.log(`🔐 Generated CSRF token for shop: ${shop}`);
      return csrfToken;

    } catch (error) {
      console.error('❌ CSRF token generation failed:', error);
      throw createError('INTERNAL_SERVER_ERROR', {
        ...context,
        shop,
        action: 'generate_csrf_token'
      });
    }
  }

  /**
   * Verify a CSRF token
   */
  async verifyToken(token: string, shop: string, context?: ErrorContext): Promise<CSRFValidationResult> {
    try {
      if (!token || typeof token !== 'string') {
        return { isValid: false, error: 'Invalid token format' };
      }

      const parts = token.split('.');
      if (parts.length !== 2) {
        return { isValid: false, error: 'Invalid token structure' };
      }

      const [payloadBase64, signature] = parts;

      // Decode payload
      let tokenData: CSRFTokenData;
      try {
        const payload = Buffer.from(payloadBase64, 'base64').toString('utf8');
        tokenData = JSON.parse(payload);
      } catch {
        return { isValid: false, error: 'Invalid payload' };
      }

      // Verify shop matches
      if (tokenData.shop !== shop) {
        return { isValid: false, error: 'Shop mismatch' };
      }

      // Check if token exists in database or memory
      let tokenExists = false;

      try {
        const dbToken = await db.safeQuery(async () => {
          return await db.cSRFToken.findUnique({
            where: { token }
          });
        });

        if (dbToken) {
          if (new Date() > dbToken.expires) {
            // Clean up expired token
            await db.safeQuery(async () => {
              await db.cSRFToken.delete({ where: { token } });
            });
            return { isValid: false, error: 'Token expired' };
          }
          tokenExists = true;

          // Remove token after use (one-time use)
          await db.safeQuery(async () => {
            await db.cSRFToken.delete({ where: { token } });
          });
        }
      } catch (dbError) {
        // Fallback to memory check
        const memoryToken = this.inMemoryTokens.get(token);
        if (memoryToken) {
          if (Date.now() > memoryToken.expires) {
            this.inMemoryTokens.delete(token);
            return { isValid: false, error: 'Token expired' };
          }
          if (memoryToken.shop === shop) {
            tokenExists = true;
            this.inMemoryTokens.delete(token); // One-time use
          }
        }
      }

      if (!tokenExists) {
        return { isValid: false, error: 'Token not found or already used' };
      }

      // Verify signature
      const secret = process.env.SESSION_SECRET || 'fallback-secret';
      const payload = Buffer.from(payloadBase64, 'base64').toString('utf8');
      const expectedSignature = createHash('sha256')
        .update(payload + secret + tokenData.nonce)
        .digest('hex');

      if (signature !== expectedSignature) {
        return { isValid: false, error: 'Invalid signature' };
      }

      console.log(`✅ CSRF token verified for shop: ${shop}`);
      return { isValid: true };

    } catch (error) {
      console.error('❌ CSRF verification error:', error);
      return { isValid: false, error: 'Verification failed' };
    }
  }

  /**
   * Clean up expired tokens
   */
  async cleanupExpiredTokens(): Promise<void> {
    try {
      // Clean up database tokens
      const deleted = await db.safeQuery(async () => {
        return await db.cSRFToken.deleteMany({
          where: {
            expires: {
              lt: new Date()
            }
          }
        });
      });

      if (deleted.count > 0) {
        console.log(`🧹 Cleaned up ${deleted.count} expired CSRF tokens from database`);
      }

      // Clean up memory tokens
      const now = Date.now();
      let cleaned = 0;
      for (const [token, data] of this.inMemoryTokens.entries()) {
        if (now > data.expires) {
          this.inMemoryTokens.delete(token);
          cleaned++;
        }
      }

      if (cleaned > 0) {
        console.log(`🧹 Cleaned up ${cleaned} expired CSRF tokens from memory`);
      }
    } catch (error) {
      console.error('❌ Error cleaning up expired CSRF tokens:', error);
    }
  }

  /**
   * Add CSRF token to loader data
   */
  async addTokenToData(shop: string, data: any, context?: ErrorContext): Promise<any> {
    const csrfToken = await this.generateToken(shop, context);
    return {
      ...data,
      csrfToken
    };
  }

  /**
   * Validate CSRF token from form data
   */
  async validateFromForm(formData: FormData, shop: string, context?: ErrorContext): Promise<boolean> {
    const token = formData.get('csrfToken')?.toString();

    console.log('🔐 CSRF validation - Shop:', shop);
    console.log('🔐 CSRF validation - Token present:', !!token);

    if (!token) {
      console.error('❌ CSRF validation failed: No token provided');
      return false;
    }

    const result = await this.verifyToken(token, shop, context);
    console.log('🔐 CSRF validation result:', result.isValid);
    return result.isValid;
  }
}

// Export singleton instance
export const csrfService = CSRFService.getInstance();

// Schedule cleanup of expired tokens every hour
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    csrfService.cleanupExpiredTokens();
  }, 60 * 60 * 1000); // 1 hour
}

// Legacy exports for backward compatibility
export async function generateCSRFToken(shop: string): Promise<string> {
  return await csrfService.generateToken(shop);
}

export async function verifyCSRFToken(token: string, shop: string): Promise<boolean> {
  const result = await csrfService.verifyToken(token, shop);
  return result.isValid;
}

export async function addCSRFToken(shop: string, data: any): Promise<any> {
  return await csrfService.addTokenToData(shop, data);
}

export async function validateCSRFFromForm(formData: FormData, shop: string): Promise<boolean> {
  return await csrfService.validateFromForm(formData, shop);
}
