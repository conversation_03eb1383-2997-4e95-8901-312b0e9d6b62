/**
 * Turso-Compatible Session Storage for Shopify
 * Replaces PrismaSessionStorage to work with our Turso adapter
 */

import { Session } from "@shopify/shopify-api";
import { SessionStorage } from "@shopify/shopify-app-remix/server";
import { getTursoPrismaAdapter } from "./turso-prisma-adapter.server";

export class TursoSessionStorage implements SessionStorage {
  private db: any;

  constructor() {
    this.db = getTursoPrismaAdapter();
    console.log('🔐 TursoSessionStorage initialized');
  }

  async storeSession(session: Session): Promise<boolean> {
    try {
      console.log(`🔐 Storing session for shop: ${session.shop}`);
      
      const sessionData = {
        id: session.id,
        shop: session.shop,
        state: session.state,
        isOnline: session.isOnline,
        scope: session.scope,
        expires: session.expires,
        accessToken: session.accessToken,
        userId: session.onlineAccessInfo?.associated_user?.id,
        firstName: session.onlineAccessInfo?.associated_user?.first_name,
        lastName: session.onlineAccessInfo?.associated_user?.last_name,
        email: session.onlineAccessInfo?.associated_user?.email,
        accountOwner: session.onlineAccessInfo?.associated_user?.account_owner || false,
        locale: session.onlineAccessInfo?.associated_user?.locale,
        collaborator: session.onlineAccessInfo?.associated_user?.collaborator || false,
        emailVerified: session.onlineAccessInfo?.associated_user?.email_verified || false
      };

      // Use upsert to handle both create and update
      await this.db.session.upsert({
        where: { id: session.id },
        update: sessionData,
        create: sessionData
      });

      console.log(`✅ Session stored successfully for shop: ${session.shop}`);
      return true;
    } catch (error: any) {
      console.error(`❌ Failed to store session for shop ${session.shop}:`, error);

      // Log specific error details for debugging
      if (error.message?.includes('401') || error.cause?.status === 401) {
        console.error('🔍 Database authentication error - check Turso credentials');
        console.error('   - Verify TURSO_DATABASE_URL is correct');
        console.error('   - Verify TURSO_AUTH_TOKEN is valid and not expired');
        console.error('   - Check token permissions for this database');
      }

      return false;
    }
  }

  async loadSession(id: string): Promise<Session | undefined> {
    try {
      console.log(`🔐 Loading session: ${id}`);
      
      const sessionData = await this.db.session.findUnique({
        where: { id }
      });

      if (!sessionData) {
        console.log(`⚠️ Session not found: ${id}`);
        return undefined;
      }

      // Convert database record back to Session object
      const session = new Session({
        id: sessionData.id,
        shop: sessionData.shop,
        state: sessionData.state,
        isOnline: Boolean(sessionData.isOnline),
        scope: sessionData.scope,
        expires: sessionData.expires ? new Date(sessionData.expires) : undefined,
        accessToken: sessionData.accessToken,
        onlineAccessInfo: sessionData.userId ? {
          associated_user: {
            id: sessionData.userId,
            first_name: sessionData.firstName,
            last_name: sessionData.lastName,
            email: sessionData.email,
            account_owner: Boolean(sessionData.accountOwner),
            locale: sessionData.locale,
            collaborator: Boolean(sessionData.collaborator),
            email_verified: Boolean(sessionData.emailVerified)
          }
        } : undefined
      });

      console.log(`✅ Session loaded successfully: ${id}`);
      return session;
    } catch (error: any) {
      console.error(`❌ Failed to load session ${id}:`, error);

      // Log specific error details for debugging
      if (error.message?.includes('401') || error.cause?.status === 401) {
        console.error('🔍 Database authentication error during session load');
      }

      return undefined;
    }
  }

  async deleteSession(id: string): Promise<boolean> {
    try {
      console.log(`🔐 Deleting session: ${id}`);
      
      await this.db.session.delete({
        where: { id }
      });

      console.log(`✅ Session deleted successfully: ${id}`);
      return true;
    } catch (error: any) {
      console.error(`❌ Failed to delete session ${id}:`, error);

      // Log specific error details for debugging
      if (error.message?.includes('401') || error.cause?.status === 401) {
        console.error('🔍 Database authentication error during session delete');
      }

      return false;
    }
  }

  async deleteSessions(ids: string[]): Promise<boolean> {
    try {
      console.log(`🔐 Deleting ${ids.length} sessions`);
      
      // Since our adapter might not support deleteMany, delete one by one
      for (const id of ids) {
        await this.deleteSession(id);
      }

      console.log(`✅ ${ids.length} sessions deleted successfully`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to delete sessions:`, error);
      return false;
    }
  }

  async findSessionsByShop(shop: string): Promise<Session[]> {
    try {
      console.log(`🔐 Finding sessions for shop: ${shop}`);
      
      // For now, return empty array as this method is not commonly used
      // In a full implementation, you'd query by shop field
      console.log(`⚠️ findSessionsByShop not fully implemented for shop: ${shop}`);
      return [];
    } catch (error) {
      console.error(`❌ Failed to find sessions for shop ${shop}:`, error);
      return [];
    }
  }
}

// Export singleton instance
let sessionStorageInstance: TursoSessionStorage | null = null;

export function getTursoSessionStorage(): TursoSessionStorage {
  if (!sessionStorageInstance) {
    sessionStorageInstance = new TursoSessionStorage();
  }
  return sessionStorageInstance;
}
