import * as React from "react";
import { useFetcher } from "@remix-run/react";
import { Button as UIButton } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  CreditCard,
  TrendingUp,
  AlertTriangle,
  Clock,
  DollarSign,
  Zap,
  CheckCircle
} from "lucide-react";
import { BillingPlan, SubscriptionData } from "../../services/billing.server";
import { ModernBillingDashboard } from "./ModernBillingDashboard";
import { PricingSelection } from "./PricingSelection";

interface BillingDashboardProps {
  subscription?: SubscriptionData;
  plan?: BillingPlan;
  trialExpired?: boolean;
  monthlyUsage?: {
    productsOptimized: number;
    totalSpent: number;
    lastOptimization?: string;
  };
  recentPurchases?: Array<{
    id: string;
    amount: number;
    productCount: number;
    date: string;
    status: string;
  }>;
  plans?: BillingPlan[];
  csrfToken?: string;
  creditBalance?: {
    totalCredits: number;
    usedCredits: number;
    remainingCredits: number;
    lastUpdated: Date;
  };
  creditHistory?: Array<{
    type: string;
    amount: number;
    description: string;
    date: Date;
    referenceId?: string;
  }>;
}

export function BillingDashboard({
  subscription,
  plan,
  trialExpired,
  monthlyUsage,
  recentPurchases = [],
  plans = [],
  csrfToken,
  creditBalance,
  creditHistory = []
}: BillingDashboardProps) {
  const fetcher = useFetcher();
  const [showPricingSelection, setShowPricingSelection] = React.useState(false);
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  const [lastRefresh, setLastRefresh] = React.useState<Date>(new Date());

  // Handle fetcher state changes and errors
  React.useEffect(() => {
    if (fetcher.data && typeof fetcher.data === 'object') {
      const data = fetcher.data as any;

      if (data.error) {
        alert(`Error: ${data.error}`);
      } else if (data.success) {
        setLastRefresh(new Date());
        if (data.redirectTo) {
          window.location.href = data.redirectTo;
        } else {
          window.location.reload();
        }
      }
    }

    setIsRefreshing(fetcher.state === 'loading' || fetcher.state === 'submitting');
  }, [fetcher.data, fetcher.state]);

  const handleCancelSubscription = () => {
    if (!subscription) {
      alert('No active subscription to cancel');
      return;
    }

    if (fetcher.state === 'submitting' || fetcher.state === 'loading') {
      console.log('⏳ Already processing request...');
      return;
    }

    const confirmMessage = `Are you sure you want to cancel your ${plan?.name || 'subscription'}? This action cannot be undone.`;

    if (confirm(confirmMessage)) {
      console.log('🔄 Cancelling subscription:', subscription.id);

      const formData = new FormData();
      formData.append('action', 'cancel_subscription');
      formData.append('subscriptionId', subscription.id);
      if (csrfToken) {
        formData.append('csrfToken', csrfToken);
      }

      fetcher.submit(formData, { method: 'POST', action: '/app/billing' });
    }
  };

  const handleRefreshData = () => {
    if (isRefreshing) {
      return;
    }

    console.log('🔄 Refreshing billing data...');
    setIsRefreshing(true);

    // Refresh the page to get latest data
    window.location.reload();
  };



  const getStatusBadge = () => {
    if (!subscription) {
      return <Badge variant="secondary">No Active Plan</Badge>;
    }

    if (trialExpired) {
      return <Badge variant="destructive">Trial Expired</Badge>;
    }

    switch (subscription.status) {
      case 'ACTIVE':
        return <Badge variant="default" className="bg-green-500">Active</Badge>;
      case 'PENDING':
        return subscription.trialDays && subscription.trialDays > 0 
          ? <Badge variant="secondary">Free Trial</Badge>
          : <Badge variant="destructive">Payment Required</Badge>;
      case 'CANCELLED':
        return <Badge variant="secondary">Cancelled</Badge>;
      default:
        return <Badge variant="secondary">{subscription.status}</Badge>;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const calculateTrialDaysRemaining = () => {
    if (!subscription?.trialDays || subscription.trialDays <= 0) return 0;
    return subscription.trialDays;
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold mb-2">Billing Dashboard</h1>
          <p className="text-muted-foreground">
            Manage your subscription and view usage statistics
          </p>
        </div>
        <div className="flex gap-3">
          <UIButton
            className="border-2 border-gray-300 bg-transparent text-black hover:bg-black hover:text-white"
            onClick={() => setShowPricingSelection(!showPricingSelection)}
          >
            {showPricingSelection ? 'Hide Plans' : 'Change Plan'}
          </UIButton>
          {subscription && subscription.status === 'ACTIVE' && (
            <UIButton
              className="bg-red-600 text-white hover:bg-red-700"
              onClick={handleCancelSubscription}
              disabled={fetcher.state === 'submitting'}
            >
              {fetcher.state === 'submitting' ? 'Cancelling...' : 'Cancel Subscription'}
            </UIButton>
          )}
        </div>
      </div>

      {/* Current Plan Status */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="w-5 h-5" />
                Current Plan
              </CardTitle>
              <CardDescription>
                Your active billing plan and status
                {lastRefresh && (
                  <span className="text-xs text-muted-foreground ml-2">
                    Last updated: {lastRefresh.toLocaleTimeString()}
                  </span>
                )}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {getStatusBadge()}
              <UIButton
                onClick={handleRefreshData}
                disabled={isRefreshing}
                className="ml-2 text-xs px-2 py-1 h-auto"
              >
                {isRefreshing ? (
                  <>
                    <Clock className="w-4 h-4 mr-1 animate-spin" />
                    Refreshing...
                  </>
                ) : (
                  <>
                    <TrendingUp className="w-4 h-4 mr-1" />
                    Refresh
                  </>
                )}
              </UIButton>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Plan Details</h4>
              <p className="text-2xl font-bold mb-1">
                {plan?.name || 'No Active Plan'}
              </p>
              {plan && (
                <p className="text-muted-foreground">
                  {plan.type === 'pay_per_use' 
                    ? `$${plan.price} per product`
                    : `$${plan.price}/${plan.type === 'annual' ? 'year' : 'month'}`
                  }
                </p>
              )}
            </div>

            {subscription && (
              <>
                <div>
                  <h4 className="font-semibold mb-2">Billing Period</h4>
                  <p className="text-sm text-muted-foreground">
                    Current period ends: {formatDate(subscription.currentPeriodEnd)}
                  </p>
                  {subscription.trialDays && subscription.trialDays > 0 && (
                    <p className="text-sm text-blue-600 font-medium mt-1">
                      <Clock className="w-4 h-4 inline mr-1" />
                      {calculateTrialDaysRemaining()} day{calculateTrialDaysRemaining() !== 1 ? 's' : ''} trial remaining
                    </p>
                  )}
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Next Billing</h4>
                  <p className="text-sm text-muted-foreground">
                    {subscription.status === 'ACTIVE' 
                      ? `Next charge: ${formatDate(subscription.currentPeriodEnd)}`
                      : trialExpired 
                        ? 'Payment required to continue'
                        : 'After trial period ends'
                    }
                  </p>
                </div>
              </>
            )}
          </div>

          {trialExpired && (
            <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2 text-red-700">
                <AlertTriangle className="w-5 h-5" />
                <span className="font-semibold">Trial Expired</span>
              </div>
              <p className="text-red-600 text-sm mt-1">
                Your free trial has ended. Please complete payment to continue using the app.
              </p>
              <UIButton
                className="mt-3 h-10 px-6 py-2 text-xs"
                onClick={() => setShowPricingSelection(!showPricingSelection)}
              >
                {showPricingSelection ? 'Hide Plans' : 'Complete Payment'}
              </UIButton>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Usage Statistics */}
      {monthlyUsage && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Usage This Month
            </CardTitle>
            <CardDescription>Your optimization activity and spending</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {monthlyUsage.productsOptimized}
                </div>
                <p className="text-sm text-muted-foreground">Products Optimized</p>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  ${monthlyUsage.totalSpent.toFixed(2)}
                </div>
                <p className="text-sm text-muted-foreground">
                  {plan?.type === 'pay_per_use' ? 'Total Spent' : 'Subscription Value'}
                </p>
              </div>
              
              <div className="text-center">
                <div className="text-lg font-semibold mb-2">
                  {monthlyUsage.lastOptimization 
                    ? formatDate(monthlyUsage.lastOptimization)
                    : 'Never'
                  }
                </div>
                <p className="text-sm text-muted-foreground">Last Optimization</p>
              </div>
            </div>

            {plan?.type === 'pay_per_use' && monthlyUsage.productsOptimized > 0 && (
              <div className="mt-6">
                <div className="flex justify-between text-sm mb-2">
                  <span>Average cost per product</span>
                  <span>${(monthlyUsage.totalSpent / monthlyUsage.productsOptimized).toFixed(2)}</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Recent Purchases (for pay-per-use) */}
      {plan?.type === 'pay_per_use' && recentPurchases.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              Recent Purchases
            </CardTitle>
            <CardDescription>Your recent pay-per-use transactions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentPurchases.slice(0, 5).map((purchase) => (
                <div key={purchase.id} className="flex justify-between items-center p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Zap className="w-4 h-4 text-green-500" />
                    <div>
                      <p className="font-medium">
                        {purchase.productCount} product{purchase.productCount > 1 ? 's' : ''} optimized
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {formatDate(purchase.date)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">${purchase.amount.toFixed(2)}</p>
                    <Badge 
                      variant={purchase.status === 'ACCEPTED' ? 'default' : 'secondary'}
                      className="text-xs"
                    >
                      {purchase.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Plan Features */}
      {plan && (
        <Card>
          <CardHeader>
            <CardTitle>Plan Features</CardTitle>
            <CardDescription>What's included in your current plan</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {plan.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Help Section */}
      <Card>
        <CardHeader>
          <CardTitle>Need Help?</CardTitle>
          <CardDescription>Get support with your billing or subscription</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold mb-2">Billing Questions</h4>
              <p className="text-sm text-muted-foreground mb-3">
                Have questions about your bill or need to update payment information?
              </p>
              <UIButton className="h-10 px-6 py-2 text-xs border-2 border-gray-300 bg-transparent text-black hover:bg-black hover:text-white">
                Contact Support
              </UIButton>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Plan Changes</h4>
              <p className="text-sm text-muted-foreground mb-3">
                Want to upgrade, downgrade, or switch to a different billing model?
              </p>
              <UIButton
                className="h-10 px-6 py-2 text-xs border-2 border-gray-300 bg-transparent text-black hover:bg-black hover:text-white"
                onClick={() => setShowPricingSelection(!showPricingSelection)}
              >
                {showPricingSelection ? 'Hide Plans' : 'View Plans'}
              </UIButton>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pricing Selection */}
      {showPricingSelection && (
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Choose Your Plan</CardTitle>
              <CardDescription>Select the plan that best fits your needs</CardDescription>
            </CardHeader>
            <CardContent>
              <PricingSelection
                plans={plans}
                selectedPlan={plan?.id}
                csrfToken={csrfToken}
                hasActiveSubscription={subscription?.status === 'ACTIVE'}
              />
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
