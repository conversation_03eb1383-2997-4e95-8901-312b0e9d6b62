/**
 * Debug Turso Connection Issues
 * Comprehensive testing and debugging for Turso database connectivity
 */

import { createClient } from '@libsql/client';
import { config } from 'dotenv';

// Load environment variables
config();

const TURSO_DATABASE_URL = process.env.TURSO_DATABASE_URL;
const TURSO_AUTH_TOKEN = process.env.TURSO_AUTH_TOKEN;

console.log('🔍 TURSO DEBUG ANALYSIS');
console.log('========================');
console.log('Node.js Version:', process.version);
console.log('Environment:', process.env.NODE_ENV);
console.log('Platform:', process.platform);
console.log('');

// Check environment variables
console.log('📋 Environment Variables:');
console.log('TURSO_DATABASE_URL:', TURSO_DATABASE_URL ? 'SET' : 'MISSING');
console.log('TURSO_AUTH_TOKEN:', TURSO_AUTH_TOKEN ? 'SET' : 'MISSING');

if (TURSO_DATABASE_URL) {
  console.log('Database URL Length:', TURSO_DATABASE_URL.length);
  console.log('Database URL Prefix:', TURSO_DATABASE_URL.substring(0, 30) + '...');
  console.log('URL Protocol:', TURSO_DATABASE_URL.split('://')[0]);
}

if (TURSO_AUTH_TOKEN) {
  console.log('Auth Token Length:', TURSO_AUTH_TOKEN.length);
  console.log('Auth Token Prefix:', TURSO_AUTH_TOKEN.substring(0, 20) + '...');
  console.log('Token Format Check:', TURSO_AUTH_TOKEN.startsWith('eyJ') ? 'JWT-like' : 'Custom');
}

console.log('');

if (!TURSO_DATABASE_URL || !TURSO_AUTH_TOKEN) {
  console.error('❌ Missing required environment variables!');
  console.error('Please set TURSO_DATABASE_URL and TURSO_AUTH_TOKEN');
  process.exit(1);
}

async function debugTursoConnection() {
  console.log('🔗 Creating Turso client...');
  
  try {
    // Test different client configurations
    const configs = [
      {
        name: 'Basic Configuration',
        config: {
          url: TURSO_DATABASE_URL,
          authToken: TURSO_AUTH_TOKEN,
        }
      },
      {
        name: 'With Sync Configuration',
        config: {
          url: TURSO_DATABASE_URL,
          authToken: TURSO_AUTH_TOKEN,
          syncUrl: TURSO_DATABASE_URL,
          syncInterval: 60,
        }
      },
      {
        name: 'With Connection Options',
        config: {
          url: TURSO_DATABASE_URL,
          authToken: TURSO_AUTH_TOKEN,
          syncUrl: TURSO_DATABASE_URL,
          syncInterval: 60,
          encryptionKey: undefined,
        }
      }
    ];

    for (const { name, config } of configs) {
      console.log(`\n🧪 Testing: ${name}`);
      console.log('─'.repeat(50));
      
      try {
        const client = createClient(config);
        console.log('✅ Client created successfully');

        // Test basic connectivity
        console.log('🔍 Testing basic query...');
        const basicResult = await client.execute('SELECT 1 as test, datetime("now") as timestamp');
        console.log('✅ Basic query successful:', basicResult.rows[0]);

        // Test table operations
        console.log('🔍 Testing table operations...');
        
        // Create test table
        await client.execute(`
          CREATE TABLE IF NOT EXISTS debug_test (
            id INTEGER PRIMARY KEY,
            message TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `);
        console.log('✅ Table creation successful');

        // Insert test data
        const insertResult = await client.execute({
          sql: 'INSERT INTO debug_test (message) VALUES (?) RETURNING id',
          args: [`Debug test ${Date.now()}`]
        });
        console.log('✅ Insert successful:', insertResult.rows[0]);

        // Query test data
        const selectResult = await client.execute('SELECT * FROM debug_test ORDER BY created_at DESC LIMIT 1');
        console.log('✅ Select successful:', selectResult.rows[0]);

        // Test session-like operations
        console.log('🔍 Testing session-like operations...');
        
        const sessionId = `test_session_${Date.now()}`;
        await client.execute({
          sql: `INSERT OR REPLACE INTO debug_test (id, message) VALUES (?, ?)`,
          args: [999, `Session test: ${sessionId}`]
        });
        console.log('✅ Upsert operation successful');

        // Cleanup
        await client.execute('DELETE FROM debug_test WHERE id = 999');
        await client.execute('DROP TABLE debug_test');
        console.log('✅ Cleanup successful');

        client.close();
        console.log(`🎉 ${name} - ALL TESTS PASSED!`);
        
        // If we get here with basic config, the connection works
        if (name === 'Basic Configuration') {
          console.log('\n✅ DIAGNOSIS: Turso connection is working correctly!');
          console.log('🔍 The issue is likely in the application code, not the credentials.');
          break;
        }
        
      } catch (error) {
        console.error(`❌ ${name} failed:`, error.message);
        
        if (error.message.includes('401')) {
          console.error('🔍 HTTP 401 - Authentication failed');
          console.error('   Possible causes:');
          console.error('   1. Auth token is expired');
          console.error('   2. Auth token doesn\'t have access to this database');
          console.error('   3. Database URL is incorrect');
        } else if (error.message.includes('404')) {
          console.error('🔍 HTTP 404 - Database not found');
          console.error('   Check if the database URL is correct');
        } else if (error.message.includes('network')) {
          console.error('🔍 Network error - connectivity issue');
        }
        
        console.error('   Full error:', error);
      }
    }

  } catch (error) {
    console.error('❌ Failed to create any Turso client:', error);
    console.error('');
    console.error('🔧 TROUBLESHOOTING STEPS:');
    console.error('1. Verify your Turso credentials are correct');
    console.error('2. Check if the database exists: turso db list');
    console.error('3. Generate a new token: turso db tokens create <database-name>');
    console.error('4. Verify token permissions: turso db tokens list <database-name>');
    console.error('5. Test connection with Turso CLI: turso db shell <database-name>');
  }
}

// Run the debug
debugTursoConnection().catch(console.error);
