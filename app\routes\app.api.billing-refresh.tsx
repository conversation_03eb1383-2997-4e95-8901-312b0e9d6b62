import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { BillingService } from "../services/billing.server";
import { invalidateBillingCache } from "../utils/cache.server";

/**
 * API endpoint to manually refresh billing status and clear cache
 * Useful for debugging billing issues or forcing fresh data
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const { admin, session } = await authenticate.admin(request);
    
    if (!session?.shop) {
      return json({ error: "No shop found in session" }, { status: 401 });
    }

    console.log(`🔄 Manual billing refresh requested for shop: ${session.shop}`);

    // Invalidate all billing-related cache
    invalidateBillingCache(session.shop);
    console.log(`🔄 Billing cache invalidated for shop: ${session.shop}`);

    // Force fresh billing status check
    const billingService = new BillingService(admin, session.shop);
    const billingStatus = await billingService.hasActiveBilling();
    
    console.log(`✅ Fresh billing status retrieved for shop: ${session.shop}`, {
      hasAccess: billingStatus.hasAccess,
      plan: billingStatus.plan?.id,
      subscription: billingStatus.subscription?.id
    });

    return json({
      success: true,
      billingStatus,
      message: "Billing status refreshed successfully"
    });

  } catch (error) {
    console.error('❌ Billing refresh error:', error);
    return json({
      error: error instanceof Error ? error.message : "Failed to refresh billing status"
    }, { status: 500 });
  }
};

export const loader = async () => {
  return json({ error: "Method not allowed" }, { status: 405 });
};
