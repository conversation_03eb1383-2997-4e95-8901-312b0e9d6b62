-- Add billing-related fields to sessions table
ALTER TABLE sessions ADD COLUMN IF NOT EXISTS subscription_id VARCHAR(255);
ALTER TABLE sessions ADD COLUMN IF NOT EXISTS subscription_status VARCHAR(50);
ALTER TABLE sessions ADD COLUMN IF NOT EXISTS billing_plan_id VARCHAR(50);
ALTER TABLE sessions ADD COLUMN IF NOT EXISTS trial_ends_at TIMESTAMP;
ALTER TABLE sessions ADD COLUMN IF NOT EXISTS last_billing_check TIMESTAMP;

-- Create billing_subscriptions table
CREATE TABLE IF NOT EXISTS billing_subscriptions (
  id SERIAL PRIMARY KEY,
  shop VARCHAR(255) NOT NULL,
  subscription_id VARCHAR(255) UNIQUE NOT NULL,
  plan_id VARCHAR(50) NOT NULL, -- 'annual', 'monthly', 'pay_per_use'
  status VARCHAR(50) NOT NULL, -- 'ACTIVE', 'PENDING', 'CANCELLED', 'EXPIRED'
  trial_days INTEGER DEFAULT 0,
  trial_ends_at TIMESTAMP,
  current_period_start TIMESTAMP,
  current_period_end TIMESTAMP,
  price_amount DECIMAL(10,2),
  price_currency VARCHAR(3) DEFAULT 'USD',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  CONSTRAINT fk_billing_shop FOREIGN KEY (shop) REFERENCES sessions(shop) ON DELETE CASCADE
);

-- Create billing_purchases table for one-time purchases (pay-per-use)
CREATE TABLE IF NOT EXISTS billing_purchases (
  id SERIAL PRIMARY KEY,
  shop VARCHAR(255) NOT NULL,
  purchase_id VARCHAR(255) UNIQUE NOT NULL,
  status VARCHAR(50) NOT NULL, -- 'PENDING', 'ACCEPTED', 'DECLINED'
  product_count INTEGER NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  description TEXT,
  optimization_batch_id VARCHAR(255), -- Link to optimization session
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  CONSTRAINT fk_purchase_shop FOREIGN KEY (shop) REFERENCES sessions(shop) ON DELETE CASCADE
);

-- Create billing_usage table to track usage for analytics
CREATE TABLE IF NOT EXISTS billing_usage (
  id SERIAL PRIMARY KEY,
  shop VARCHAR(255) NOT NULL,
  billing_type VARCHAR(20) NOT NULL, -- 'subscription', 'pay_per_use'
  billing_reference_id VARCHAR(255), -- subscription_id or purchase_id
  products_optimized INTEGER NOT NULL,
  optimization_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  batch_id VARCHAR(255), -- Group optimizations by batch
  
  CONSTRAINT fk_usage_shop FOREIGN KEY (shop) REFERENCES sessions(shop) ON DELETE CASCADE
);

-- Create billing_events table for audit trail
CREATE TABLE IF NOT EXISTS billing_events (
  id SERIAL PRIMARY KEY,
  shop VARCHAR(255) NOT NULL,
  event_type VARCHAR(50) NOT NULL, -- 'subscription_created', 'subscription_cancelled', 'purchase_created', etc.
  reference_id VARCHAR(255), -- subscription_id or purchase_id
  event_data JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  CONSTRAINT fk_events_shop FOREIGN KEY (shop) REFERENCES sessions(shop) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_billing_subscriptions_shop ON billing_subscriptions(shop);
CREATE INDEX IF NOT EXISTS idx_billing_subscriptions_status ON billing_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_billing_purchases_shop ON billing_purchases(shop);
CREATE INDEX IF NOT EXISTS idx_billing_purchases_status ON billing_purchases(status);
CREATE INDEX IF NOT EXISTS idx_billing_usage_shop ON billing_usage(shop);
CREATE INDEX IF NOT EXISTS idx_billing_usage_date ON billing_usage(optimization_date);
CREATE INDEX IF NOT EXISTS idx_billing_events_shop ON billing_events(shop);
CREATE INDEX IF NOT EXISTS idx_billing_events_type ON billing_events(event_type);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_billing_subscriptions_updated_at 
  BEFORE UPDATE ON billing_subscriptions 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_billing_purchases_updated_at 
  BEFORE UPDATE ON billing_purchases 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
