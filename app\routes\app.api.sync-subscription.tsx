import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { BillingService } from "../services/billing.server";
import { invalidateBillingCache } from "../utils/cache.server";
import { getTursoPrismaAdapter } from "../lib/turso-prisma-adapter.server";

const db = getTursoPrismaAdapter();

/**
 * Manual subscription sync endpoint
 * Forces a fresh check of Shopify subscriptions and updates local database
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const { admin, session } = await authenticate.admin(request);
    
    if (!session?.shop) {
      return json({ error: "No shop found in session" }, { status: 401 });
    }

    console.log(`🔄 Manual subscription sync requested for shop: ${session.shop}`);

    // Clear all billing cache first
    invalidateBillingCache(session.shop);
    console.log(`🔄 All billing cache cleared for shop: ${session.shop}`);

    // Get fresh subscription data from Shopify
    const billingService = new BillingService(admin, session.shop);
    const subscriptionData = await billingService.getCurrentSubscription();
    const allSubscriptions = subscriptionData.data?.currentAppInstallation?.activeSubscriptions || [];
    
    console.log(`📊 Found ${allSubscriptions.length} subscriptions from Shopify API`);
    
    if (allSubscriptions.length > 0) {
      const subscription = allSubscriptions[0];
      console.log(`📋 Processing subscription: ${subscription.id}, status: ${subscription.status}`);
      
      // Determine plan ID
      const determinePlanId = (sub: any): string => {
        if (!sub.name) return 'monthly';
        const name = sub.name.toLowerCase();
        if (name.includes('annual') || name.includes('yearly')) return 'annual';
        return 'monthly';
      };

      // Update database with subscription info
      await db.$transaction(async (tx) => {
        // Update or create billing subscription
        await tx.billingSubscription.upsert({
          where: { subscriptionId: subscription.id },
          update: {
            status: subscription.status,
            planId: determinePlanId(subscription),
            trialDays: subscription.trialDays || 0,
            currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
            updatedAt: new Date()
          },
          create: {
            shop: session.shop,
            subscriptionId: subscription.id,
            status: subscription.status,
            planId: determinePlanId(subscription),
            trialDays: subscription.trialDays || 0,
            trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1000) : null,
            currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
            priceAmount: 29.99, // Default price
            priceCurrency: 'USD'
          }
        });

        // Update session with subscription info
        await tx.session.updateMany({
          where: { shop: session.shop },
          data: {
            subscriptionId: subscription.id,
            subscriptionStatus: subscription.status,
            billingPlanId: determinePlanId(subscription),
            trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1000) : null,
            lastBillingCheck: new Date()
          }
        });

        // Log sync event
        await tx.billingEvent.create({
          data: {
            shop: session.shop,
            eventType: 'subscription_manual_sync',
            referenceId: subscription.id,
            eventData: JSON.stringify({
              subscriptionId: subscription.id,
              status: subscription.status,
              planId: determinePlanId(subscription),
              syncTimestamp: new Date().toISOString()
            })
          }
        });
      });

      console.log(`✅ Subscription synced successfully for shop: ${session.shop}`);
      
      // Get fresh billing status after sync
      const billingStatus = await billingService.hasActiveBilling();
      
      return json({
        success: true,
        message: "Subscription synced successfully",
        subscription: {
          id: subscription.id,
          status: subscription.status,
          planId: determinePlanId(subscription)
        },
        billingStatus
      });
    } else {
      console.log(`❌ No active subscriptions found for shop: ${session.shop}`);
      return json({
        success: false,
        message: "No active subscriptions found in Shopify",
        subscriptionsFound: 0
      });
    }

  } catch (error) {
    console.error('❌ Subscription sync error:', error);
    return json({
      error: error instanceof Error ? error.message : "Failed to sync subscription"
    }, { status: 500 });
  }
};

export const loader = async () => {
  return json({ error: "Method not allowed" }, { status: 405 });
};
