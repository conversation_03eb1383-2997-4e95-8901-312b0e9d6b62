import React from "react";

interface CreditBalance {
  totalCredits: number;
  usedCredits: number;
  remainingCredits: number;
  lastUpdated: Date;
}

interface CreditTransaction {
  type: string;
  amount: number;
  description: string;
  date: Date;
  referenceId?: string;
}

interface CreditsDashboardProps {
  balance: CreditBalance;
  history: CreditTransaction[];
  hasSubscription: boolean;
}

export function CreditsDashboard({ balance, history, hasSubscription }: CreditsDashboardProps) {
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'PURCHASE': return '💳';
      case 'USAGE': return '🔧';
      case 'REFUND': return '↩️';
      case 'BONUS': return '🎁';
      default: return '📝';
    }
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'PURCHASE': return 'text-green-400';
      case 'USAGE': return 'text-blue-400';
      case 'REFUND': return 'text-yellow-400';
      case 'BONUS': return 'text-purple-400';
      default: return 'text-gray-400';
    }
  };

  if (hasSubscription) {
    return (
      <div className="bg-white/10 border border-white/20 rounded-3xl p-8">
        <div className="text-center">
          <div className="text-6xl mb-4">🚀</div>
          <h2 className="text-3xl font-bold mb-2">Unlimited Access</h2>
          <p className="text-white/70 text-lg">
            You have an active subscription with unlimited product optimizations
          </p>
          <div className="mt-6 inline-flex items-center bg-green-500/20 border border-green-500/40 rounded-2xl px-6 py-3">
            <div className="text-green-300 font-bold">✅ Active Subscription</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Credits Balance */}
      <div className="bg-white/10 border border-white/20 rounded-3xl p-8">
        <h2 className="text-2xl font-bold mb-6">Optimization Credits</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="text-center">
            <div className="text-4xl font-bold mb-2 text-blue-400">
              {balance.totalCredits}
            </div>
            <div className="text-white/70">Total Purchased</div>
          </div>
          
          <div className="text-center">
            <div className="text-4xl font-bold mb-2 text-orange-400">
              {balance.usedCredits}
            </div>
            <div className="text-white/70">Used</div>
          </div>
          
          <div className="text-center">
            <div className="text-4xl font-bold mb-2 text-green-400">
              {balance.remainingCredits}
            </div>
            <div className="text-white/70">Remaining</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-white/70 mb-2">
            <span>Credits Used</span>
            <span>{balance.usedCredits} / {balance.totalCredits}</span>
          </div>
          <div className="w-full bg-white/10 rounded-full h-3">
            <div 
              className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-300"
              style={{ 
                width: balance.totalCredits > 0 
                  ? `${Math.min(100, (balance.usedCredits / balance.totalCredits) * 100)}%` 
                  : '0%' 
              }}
            />
          </div>
        </div>

        {/* Credit Info */}
        <div className="bg-white/5 rounded-2xl p-4">
          <div className="text-sm text-white/70">
            <div className="flex items-center justify-between mb-2">
              <span>💡 Each credit = 1 product optimization</span>
              <span>💰 $0.10 per credit</span>
            </div>
            <div className="text-xs text-white/50">
              Last updated: {formatDate(balance.lastUpdated)}
            </div>
          </div>
        </div>
      </div>

      {/* Transaction History */}
      {history.length > 0 && (
        <div className="bg-white/10 border border-white/20 rounded-3xl p-8">
          <h2 className="text-2xl font-bold mb-6">Credit History</h2>
          
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {history.map((transaction, index) => (
              <div key={index} className="flex items-center justify-between py-3 border-b border-white/10 last:border-b-0">
                <div className="flex items-center space-x-4">
                  <div className="text-2xl">
                    {getTransactionIcon(transaction.type)}
                  </div>
                  <div>
                    <div className="font-semibold">
                      {transaction.description}
                    </div>
                    <div className="text-sm text-white/60">
                      {formatDate(transaction.date)}
                      {transaction.referenceId && (
                        <span className="ml-2 text-white/40">
                          #{transaction.referenceId.substring(0, 8)}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className={`font-bold text-lg ${getTransactionColor(transaction.type)}`}>
                  {transaction.amount > 0 ? '+' : ''}{transaction.amount}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* No Credits Message */}
      {balance.totalCredits === 0 && (
        <div className="bg-white/10 border border-white/20 rounded-3xl p-8 text-center">
          <div className="text-6xl mb-4">💳</div>
          <h3 className="text-2xl font-bold mb-2">No Credits Yet</h3>
          <p className="text-white/70 mb-6">
            Purchase optimization credits to start improving your products
          </p>
          <div className="text-sm text-white/60">
            💡 Tip: Each credit optimizes one product for just $0.10
          </div>
        </div>
      )}
    </div>
  );
}
