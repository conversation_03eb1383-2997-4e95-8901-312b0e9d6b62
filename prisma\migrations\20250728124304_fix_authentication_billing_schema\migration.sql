/*
  Warnings:

  - Added the required column `updatedAt` to the `Session` table without a default value. This is not possible if the table is not empty.

*/
-- CreateTable
CREATE TABLE "CSRFToken" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "token" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "expires" DATETIME NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "RateLimitEntry" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "identifier" TEXT NOT NULL,
    "count" INTEGER NOT NULL DEFAULT 1,
    "resetTime" DATETIME NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Session" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "state" TEXT NOT NULL,
    "isOnline" BOOLEAN NOT NULL DEFAULT false,
    "scope" TEXT,
    "expires" DATETIME,
    "accessToken" TEXT NOT NULL,
    "userId" BIGINT,
    "firstName" TEXT,
    "lastName" TEXT,
    "email" TEXT,
    "accountOwner" BOOLEAN NOT NULL DEFAULT false,
    "locale" TEXT,
    "collaborator" BOOLEAN DEFAULT false,
    "emailVerified" BOOLEAN DEFAULT false,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "subscriptionId" TEXT,
    "subscriptionStatus" TEXT,
    "billingPlanId" TEXT,
    "trialEndsAt" DATETIME,
    "lastBillingCheck" DATETIME
);
INSERT INTO "new_Session" ("accessToken", "accountOwner", "billingPlanId", "collaborator", "email", "emailVerified", "expires", "firstName", "id", "isOnline", "lastBillingCheck", "lastName", "locale", "scope", "shop", "state", "subscriptionId", "subscriptionStatus", "trialEndsAt", "userId", "createdAt", "updatedAt") SELECT "accessToken", "accountOwner", "billingPlanId", "collaborator", "email", "emailVerified", "expires", "firstName", "id", "isOnline", "lastBillingCheck", "lastName", "locale", "scope", "shop", "state", "subscriptionId", "subscriptionStatus", "trialEndsAt", "userId", CURRENT_TIMESTAMP, CURRENT_TIMESTAMP FROM "Session";
DROP TABLE "Session";
ALTER TABLE "new_Session" RENAME TO "Session";
CREATE UNIQUE INDEX "Session_shop_key" ON "Session"("shop");
CREATE INDEX "Session_shop_idx" ON "Session"("shop");
CREATE INDEX "Session_expires_idx" ON "Session"("expires");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;

-- CreateIndex
CREATE UNIQUE INDEX "CSRFToken_token_key" ON "CSRFToken"("token");

-- CreateIndex
CREATE INDEX "CSRFToken_shop_idx" ON "CSRFToken"("shop");

-- CreateIndex
CREATE INDEX "CSRFToken_expires_idx" ON "CSRFToken"("expires");

-- CreateIndex
CREATE INDEX "CSRFToken_token_idx" ON "CSRFToken"("token");

-- CreateIndex
CREATE UNIQUE INDEX "RateLimitEntry_identifier_key" ON "RateLimitEntry"("identifier");

-- CreateIndex
CREATE INDEX "RateLimitEntry_identifier_idx" ON "RateLimitEntry"("identifier");

-- CreateIndex
CREATE INDEX "RateLimitEntry_resetTime_idx" ON "RateLimitEntry"("resetTime");
