/**
 * Production-Grade Turso Migration Script
 * Safely migrates database schema to Turso
 */

import { createClient } from "@libsql/client";
import fs from "fs";
import path from "path";
import { config } from "dotenv";

// Load environment variables from .env file
config();

async function migrateTurso() {
  const tursoUrl = process.env.TURSO_DATABASE_URL;
  const tursoToken = process.env.TURSO_AUTH_TOKEN;

  console.log('🔍 Debug - TURSO_DATABASE_URL:', tursoUrl ? `${tursoUrl.substring(0, 50)}...` : 'undefined');
  console.log('🔍 Debug - TURSO_AUTH_TOKEN:', tursoToken ? 'present' : 'undefined');
  console.log('🔍 Debug - NODE_ENV:', process.env.NODE_ENV);

  if (!tursoUrl || !tursoToken) {
    console.log('⚠️ Turso credentials not found, skipping Turso migration');
    console.log('This is normal for local development - using local SQLite instead');
    return;
  }

  console.log('🚀 Starting Turso migration...');

  const client = createClient({
    url: tursoUrl,
    authToken: tursoToken,
  });

  try {
    console.log('📄 Creating complete database schema...');

    // Complete schema SQL for all tables
    const schemaSql = `
      -- Session table for Shopify authentication
      CREATE TABLE IF NOT EXISTS Session (
        id TEXT PRIMARY KEY,
        shop TEXT NOT NULL UNIQUE,
        state TEXT NOT NULL,
        isOnline INTEGER NOT NULL DEFAULT 0,
        scope TEXT,
        expires TEXT,
        accessToken TEXT NOT NULL,
        userId INTEGER,
        firstName TEXT,
        lastName TEXT,
        email TEXT,
        accountOwner INTEGER NOT NULL DEFAULT 0,
        locale TEXT,
        collaborator INTEGER NOT NULL DEFAULT 0,
        emailVerified INTEGER NOT NULL DEFAULT 0,
        createdAt TEXT NOT NULL DEFAULT (datetime('now')),
        updatedAt TEXT NOT NULL DEFAULT (datetime('now'))
      );

      -- Billing tables
      CREATE TABLE IF NOT EXISTS BillingSubscription (
        id TEXT PRIMARY KEY,
        shop TEXT NOT NULL,
        planId TEXT NOT NULL,
        status TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      );

      CREATE TABLE IF NOT EXISTS BillingPurchase (
        id TEXT PRIMARY KEY,
        shop TEXT NOT NULL,
        purchaseId TEXT NOT NULL,
        status TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      );

      CREATE TABLE IF NOT EXISTS BillingUsage (
        id TEXT PRIMARY KEY,
        shop TEXT NOT NULL,
        credits INTEGER NOT NULL DEFAULT 0,
        usedCredits INTEGER NOT NULL DEFAULT 0,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      );

      CREATE TABLE IF NOT EXISTS BillingEvent (
        id TEXT PRIMARY KEY,
        shop TEXT NOT NULL,
        eventType TEXT NOT NULL,
        eventData TEXT,
        createdAt TEXT NOT NULL
      );

      -- Security and utility tables
      CREATE TABLE IF NOT EXISTS CSRFToken (
        id TEXT PRIMARY KEY,
        shop TEXT NOT NULL,
        token TEXT NOT NULL,
        expiresAt TEXT NOT NULL,
        createdAt TEXT NOT NULL
      );

      CREATE TABLE IF NOT EXISTS RateLimitEntry (
        id TEXT PRIMARY KEY,
        identifier TEXT NOT NULL,
        count INTEGER NOT NULL DEFAULT 1,
        resetTime TEXT NOT NULL,
        createdAt TEXT NOT NULL
      );

      CREATE TABLE IF NOT EXISTS CacheEntry (
        id TEXT PRIMARY KEY,
        key TEXT NOT NULL UNIQUE,
        value TEXT NOT NULL,
        expiresAt TEXT,
        createdAt TEXT NOT NULL
      );
    `;

    // Split SQL into individual statements and execute
    const statements = schemaSql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`🔄 Executing ${statements.length} schema statements...`);

    for (const statement of statements) {
      if (statement.trim()) {
        console.log(`🔄 Creating: ${statement.match(/CREATE TABLE IF NOT EXISTS (\w+)/)?.[1] || 'table'}...`);
        await client.execute(statement);
      }
    }

    console.log('✅ Schema creation completed successfully');
    
    // Verify tables exist
    const tables = await client.execute(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `);
    
    console.log('📋 Tables in database:');
    tables.rows.forEach(row => {
      console.log(`  - ${row.name}`);
    });

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    client.close();
  }
}

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  migrateTurso().catch(console.error);
}

export { migrateTurso };
