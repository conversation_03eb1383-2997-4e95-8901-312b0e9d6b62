/**
 * Production-Grade Turso Migration Script
 * Safely migrates database schema to Turso
 */

import { createClient } from "@libsql/client";
import fs from "fs";
import path from "path";
import { config } from "dotenv";

// Load environment variables from .env file
config();

async function migrateTurso() {
  const tursoUrl = process.env.TURSO_DATABASE_URL;
  const tursoToken = process.env.TURSO_AUTH_TOKEN;

  console.log('🔍 Debug - TURSO_DATABASE_URL:', tursoUrl ? `${tursoUrl.substring(0, 50)}...` : 'undefined');
  console.log('🔍 Debug - TURSO_AUTH_TOKEN:', tursoToken ? 'present' : 'undefined');
  console.log('🔍 Debug - NODE_ENV:', process.env.NODE_ENV);

  if (!tursoUrl || !tursoToken) {
    console.log('⚠️ Turso credentials not found, skipping Turso migration');
    console.log('This is normal for local development - using local SQLite instead');
    return;
  }

  console.log('🚀 Starting Turso migration...');

  const client = createClient({
    url: tursoUrl,
    authToken: tursoToken,
  });

  try {
    console.log('📄 Creating complete database schema...');

    // Complete schema SQL for all tables - MUST match Prisma schema exactly
    const schemaSql = `
      -- Session table for Shopify authentication (matches Prisma schema)
      CREATE TABLE IF NOT EXISTS Session (
        id TEXT PRIMARY KEY,
        shop TEXT NOT NULL UNIQUE,
        state TEXT NOT NULL,
        isOnline INTEGER NOT NULL DEFAULT 0,
        scope TEXT,
        expires TEXT,
        accessToken TEXT NOT NULL,
        userId INTEGER,
        firstName TEXT,
        lastName TEXT,
        email TEXT,
        accountOwner INTEGER NOT NULL DEFAULT 0,
        locale TEXT,
        collaborator INTEGER DEFAULT 0,
        emailVerified INTEGER DEFAULT 0,
        createdAt TEXT NOT NULL DEFAULT (datetime('now')),
        updatedAt TEXT NOT NULL DEFAULT (datetime('now')),
        -- Additional billing fields from Prisma schema
        subscriptionId TEXT,
        subscriptionStatus TEXT,
        billingPlanId TEXT,
        trialEndsAt TEXT,
        lastBillingCheck TEXT
      );

      -- Billing tables (matching Prisma schema exactly)
      CREATE TABLE IF NOT EXISTS BillingSubscription (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        shop TEXT NOT NULL,
        subscriptionId TEXT NOT NULL UNIQUE,
        planId TEXT NOT NULL,
        status TEXT NOT NULL,
        trialDays INTEGER NOT NULL DEFAULT 0,
        trialEndsAt TEXT,
        currentPeriodStart TEXT,
        currentPeriodEnd TEXT,
        priceAmount REAL,
        priceCurrency TEXT NOT NULL DEFAULT 'USD',
        createdAt TEXT NOT NULL DEFAULT (datetime('now')),
        updatedAt TEXT NOT NULL DEFAULT (datetime('now'))
      );

      CREATE TABLE IF NOT EXISTS BillingPurchase (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        shop TEXT NOT NULL,
        purchaseId TEXT NOT NULL UNIQUE,
        status TEXT NOT NULL,
        productCount INTEGER NOT NULL,
        amount REAL NOT NULL,
        currency TEXT NOT NULL DEFAULT 'USD',
        description TEXT,
        optimizationBatchId TEXT,
        createdAt TEXT NOT NULL DEFAULT (datetime('now')),
        updatedAt TEXT NOT NULL DEFAULT (datetime('now'))
      );

      CREATE TABLE IF NOT EXISTS BillingUsage (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        shop TEXT NOT NULL,
        billingType TEXT NOT NULL,
        billingReferenceId TEXT,
        productsOptimized INTEGER NOT NULL,
        optimizationDate TEXT NOT NULL DEFAULT (datetime('now')),
        batchId TEXT
      );

      CREATE TABLE IF NOT EXISTS BillingEvent (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        shop TEXT NOT NULL,
        eventType TEXT NOT NULL,
        referenceId TEXT,
        eventData TEXT,
        createdAt TEXT NOT NULL DEFAULT (datetime('now'))
      );

      -- Security and utility tables (matching Prisma schema exactly)
      CREATE TABLE IF NOT EXISTS CSRFToken (
        id TEXT PRIMARY KEY,
        token TEXT NOT NULL UNIQUE,
        shop TEXT NOT NULL,
        expires TEXT NOT NULL,
        createdAt TEXT NOT NULL DEFAULT (datetime('now'))
      );

      CREATE TABLE IF NOT EXISTS RateLimitEntry (
        id TEXT PRIMARY KEY,
        identifier TEXT NOT NULL UNIQUE,
        count INTEGER NOT NULL DEFAULT 1,
        resetTime TEXT NOT NULL,
        createdAt TEXT NOT NULL DEFAULT (datetime('now')),
        updatedAt TEXT NOT NULL DEFAULT (datetime('now'))
      );

      CREATE TABLE IF NOT EXISTS CacheEntry (
        id TEXT PRIMARY KEY,
        key TEXT NOT NULL UNIQUE,
        data TEXT NOT NULL,
        expiresAt TEXT NOT NULL,
        tags TEXT,
        createdAt TEXT NOT NULL DEFAULT (datetime('now')),
        updatedAt TEXT NOT NULL DEFAULT (datetime('now'))
      );
    `;

    // Split SQL into individual statements and execute
    const statements = schemaSql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`🔄 Executing ${statements.length} schema statements...`);

    for (const statement of statements) {
      if (statement.trim()) {
        console.log(`🔄 Creating: ${statement.match(/CREATE TABLE IF NOT EXISTS (\w+)/)?.[1] || 'table'}...`);
        await client.execute(statement);
      }
    }

    console.log('✅ Schema creation completed successfully');
    
    // Verify tables exist
    const tables = await client.execute(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `);
    
    console.log('📋 Tables in database:');
    tables.rows.forEach(row => {
      console.log(`  - ${row.name}`);
    });

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    client.close();
  }
}

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  migrateTurso().catch(console.error);
}

export { migrateTurso };
