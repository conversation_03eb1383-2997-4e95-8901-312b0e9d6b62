#!/usr/bin/env node

/**
 * Turso Connection Test Script
 * Tests the exact connection that's failing in production
 */

import { createClient } from '@libsql/client';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const TURSO_DATABASE_URL = process.env.TURSO_DATABASE_URL || "libsql://prod-rank-x-vercel-icfg-************************.aws-us-east-1.turso.io";
const TURSO_AUTH_TOKEN = process.env.TURSO_AUTH_TOKEN || "***************************************************************************************************************************************************************************************************************************************************************************";

console.log('🔍 TURSO CONNECTION TEST');
console.log('========================');
console.log('Database URL:', TURSO_DATABASE_URL);
console.log('Auth Token Length:', TURSO_AUTH_TOKEN.length);
console.log('Auth Token Prefix:', TURSO_AUTH_TOKEN.substring(0, 20) + '...');
console.log('');

async function testTursoConnection() {
  try {
    console.log('🔗 Creating Turso client...');
    const client = createClient({
      url: TURSO_DATABASE_URL,
      authToken: TURSO_AUTH_TOKEN,
    });

    console.log('✅ Client created successfully');

    console.log('🔍 Testing basic query...');
    const result = await client.execute('SELECT 1 as test');
    console.log('✅ Basic query successful:', result);

    console.log('🔍 Testing table creation...');
    await client.execute(`
      CREATE TABLE IF NOT EXISTS test_connection (
        id INTEGER PRIMARY KEY,
        message TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Table creation successful');

    console.log('🔍 Testing insert...');
    await client.execute({
      sql: 'INSERT INTO test_connection (message) VALUES (?)',
      args: ['Connection test successful']
    });
    console.log('✅ Insert successful');

    console.log('🔍 Testing select...');
    const selectResult = await client.execute('SELECT * FROM test_connection ORDER BY created_at DESC LIMIT 1');
    console.log('✅ Select successful:', selectResult.rows[0]);

    console.log('🔍 Testing cleanup...');
    await client.execute('DROP TABLE test_connection');
    console.log('✅ Cleanup successful');

    console.log('');
    console.log('🎉 ALL TESTS PASSED! Turso connection is working perfectly.');
    console.log('✅ The issue is likely in the application code, not the credentials.');

  } catch (error) {
    console.error('❌ CONNECTION TEST FAILED!');
    console.error('Error Type:', error.constructor.name);
    console.error('Error Code:', error.code);
    console.error('Error Message:', error.message);
    
    if (error.cause) {
      console.error('Cause:', error.cause);
    }
    
    if (error.status) {
      console.error('HTTP Status:', error.status);
    }

    console.error('Full Error:', error);

    // Specific error analysis
    if (error.message.includes('401')) {
      console.error('');
      console.error('🔍 ANALYSIS: HTTP 401 - Authentication Failed');
      console.error('Possible causes:');
      console.error('1. Auth token is expired or invalid');
      console.error('2. Auth token doesn\'t have access to this database');
      console.error('3. Database URL is incorrect');
      console.error('');
      console.error('🔧 SOLUTIONS:');
      console.error('1. Generate a new auth token: turso db tokens create prod-rank-x-vercel');
      console.error('2. Verify database name: turso db list');
      console.error('3. Check token permissions: turso db tokens list prod-rank-x-vercel');
    }

    process.exit(1);
  }
}

// Run the test
testTursoConnection().catch(console.error);
