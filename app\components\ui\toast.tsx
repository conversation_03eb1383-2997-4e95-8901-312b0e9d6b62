/**
 * Toast Notification Component
 * Provides user-friendly notifications for billing actions
 */

import * as React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from "lucide-react";
import { But<PERSON> } from "./button";
import type { UserMessage } from "../../utils/user-messages";

interface ToastProps extends UserMessage {
  id: string;
  onClose: () => void;
  duration?: number;
}

export function Toast({ id, type, title, message, action, onClose, duration = 5000 }: ToastProps) {
  const [isVisible, setIsVisible] = React.useState(true);

  React.useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        setTimeout(onClose, 300); // Wait for exit animation
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration, onClose]);

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'info':
        return <Info className="w-5 h-5 text-blue-500" />;
      default:
        return <Info className="w-5 h-5 text-gray-500" />;
    }
  };

  const getBackgroundColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      case 'info':
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: -50, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -50, scale: 0.95 }}
      className={`
        max-w-md w-full p-4 rounded-lg border shadow-lg
        ${getBackgroundColor()}
      `}
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-0.5">
          {getIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-semibold text-gray-900 mb-1">
            {title}
          </h4>
          <p className="text-sm text-gray-700 leading-relaxed">
            {message}
          </p>
          
          {action && (
            <div className="mt-3">
              <Button
                onClick={() => {
                  action.onClick();
                  onClose();
                }}
                className="text-xs px-3 py-1 h-auto"
              >
                {action.label}
              </Button>
            </div>
          )}
        </div>
        
        <button
          onClick={() => {
            setIsVisible(false);
            setTimeout(onClose, 300);
          }}
          className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    </motion.div>
  );
}

interface ToastContainerProps {
  toasts: (UserMessage & { id: string })[];
  onRemoveToast: (id: string) => void;
}

export function ToastContainer({ toasts, onRemoveToast }: ToastContainerProps) {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      <AnimatePresence>
        {toasts.map((toast) => (
          <Toast
            key={toast.id}
            {...toast}
            onClose={() => onRemoveToast(toast.id)}
          />
        ))}
      </AnimatePresence>
    </div>
  );
}

// Toast context and hook
interface ToastContextType {
  addToast: (toast: Omit<UserMessage, 'id'>) => void;
  removeToast: (id: string) => void;
  toasts: (UserMessage & { id: string })[];
}

const ToastContext = React.createContext<ToastContextType | undefined>(undefined);

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = React.useState<(UserMessage & { id: string })[]>([]);

  const addToast = React.useCallback((toast: Omit<UserMessage, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    setToasts(prev => [...prev, { ...toast, id }]);
  }, []);

  const removeToast = React.useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const value = React.useMemo(() => ({
    addToast,
    removeToast,
    toasts
  }), [addToast, removeToast, toasts]);

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer toasts={toasts} onRemoveToast={removeToast} />
    </ToastContext.Provider>
  );
}

export function useToast() {
  const context = React.useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
}

// Convenience hooks for different toast types
export function useToastHelpers() {
  const { addToast } = useToast();

  return React.useMemo(() => ({
    showSuccess: (title: string, message: string, action?: UserMessage['action']) => {
      addToast({ type: 'success', title, message, action });
    },
    showError: (title: string, message: string, action?: UserMessage['action']) => {
      addToast({ type: 'error', title, message, action });
    },
    showWarning: (title: string, message: string, action?: UserMessage['action']) => {
      addToast({ type: 'warning', title, message, action });
    },
    showInfo: (title: string, message: string, action?: UserMessage['action']) => {
      addToast({ type: 'info', title, message, action });
    },
    showBillingError: (error: string) => {
      const { getBillingErrorMessage } = require('../../utils/user-messages');
      const userMessage = getBillingErrorMessage(error);
      addToast(userMessage);
    },
    showBillingSuccess: (action: string, details?: any) => {
      const { getBillingSuccessMessage } = require('../../utils/user-messages');
      const userMessage = getBillingSuccessMessage(action, details);
      addToast(userMessage);
    }
  }), [addToast]);
}

// Loading toast component
interface LoadingToastProps {
  message: string;
  onCancel?: () => void;
}

export function LoadingToast({ message, onCancel }: LoadingToastProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-md w-full p-4 rounded-lg border bg-blue-50 border-blue-200 shadow-lg"
    >
      <div className="flex items-center gap-3">
        <div className="flex-shrink-0">
          <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        </div>
        
        <div className="flex-1">
          <p className="text-sm font-medium text-blue-900">
            {message}
          </p>
        </div>
        
        {onCancel && (
          <button
            onClick={onCancel}
            className="flex-shrink-0 text-blue-400 hover:text-blue-600 transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>
    </motion.div>
  );
}

// Confirmation dialog component
interface ConfirmationDialogProps {
  isOpen: boolean;
  title: string;
  message: string;
  confirmLabel?: string;
  cancelLabel?: string;
  onConfirm: () => void;
  onCancel: () => void;
  type?: 'danger' | 'warning' | 'info';
}

export function ConfirmationDialog({
  isOpen,
  title,
  message,
  confirmLabel = 'Confirm',
  cancelLabel = 'Cancel',
  onConfirm,
  onCancel,
  type = 'info'
}: ConfirmationDialogProps) {
  if (!isOpen) return null;

  const getIcon = () => {
    switch (type) {
      case 'danger':
        return <AlertCircle className="w-6 h-6 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-6 h-6 text-yellow-500" />;
      default:
        return <Info className="w-6 h-6 text-blue-500" />;
    }
  };

  const getConfirmButtonClass = () => {
    switch (type) {
      case 'danger':
        return 'bg-red-600 hover:bg-red-700 text-white';
      case 'warning':
        return 'bg-yellow-600 hover:bg-yellow-700 text-white';
      default:
        return 'bg-blue-600 hover:bg-blue-700 text-white';
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white rounded-lg shadow-xl max-w-md w-full p-6"
      >
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0">
            {getIcon()}
          </div>
          
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {title}
            </h3>
            <p className="text-sm text-gray-700 leading-relaxed mb-6">
              {message}
            </p>
            
            <div className="flex gap-3 justify-end">
              <Button
                onClick={onCancel}
                className="px-4 py-2 text-sm border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
              >
                {cancelLabel}
              </Button>
              <Button
                onClick={onConfirm}
                className={`px-4 py-2 text-sm ${getConfirmButtonClass()}`}
              >
                {confirmLabel}
              </Button>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
