import * as React from "react";
import { motion } from "framer-motion";

export function SettingsHeader() {
  return (
    <div className="relative bg-black text-white py-24 px-6 overflow-hidden">
      {/* Gear/Settings Pattern Background */}
      <div className="absolute inset-0 opacity-5">
        <svg className="w-full h-full" viewBox="0 0 400 400">
          <defs>
            <pattern id="settings" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
              <circle cx="40" cy="40" r="15" fill="none" stroke="white" strokeWidth="2" />
              <circle cx="40" cy="40" r="8" fill="none" stroke="white" strokeWidth="1" />
              <path d="M40 25v30M25 40h30M32 32l16 16M32 48l16-16" stroke="white" strokeWidth="1" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#settings)" />
        </svg>
      </div>

      {/* Floating Settings Icons */}
      <div className="absolute inset-0">
        {[
          { icon: "⚙️", x: "15%", y: "20%", delay: 0 },
          { icon: "🔧", x: "85%", y: "25%", delay: 0.5 },
          { icon: "⚡", x: "10%", y: "70%", delay: 1 },
          { icon: "🎯", x: "90%", y: "75%", delay: 1.5 },
          { icon: "🔑", x: "20%", y: "45%", delay: 2 },
          { icon: "📊", x: "80%", y: "50%", delay: 2.5 }
        ].map((item, i) => (
          <motion.div
            key={i}
            className="absolute text-2xl"
            style={{ left: item.x, top: item.y }}
            animate={{
              opacity: [0.3, 0.8, 0.3],
              rotate: [0, 180, 360],
              scale: [0.8, 1.2, 0.8],
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              delay: item.delay,
              ease: "easeInOut"
            }}
          >
            {item.icon}
          </motion.div>
        ))}
      </div>

      <div className="max-w-5xl mx-auto text-center relative z-10">
        {/* Header Badge */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="inline-flex items-center bg-white/10 rounded-full px-6 py-3 mb-8"
        >
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" />
            <span className="font-bold text-sm tracking-wide">CONFIGURATION</span>
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
            <span className="font-bold text-sm tracking-wide">PREFERENCES</span>
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
          </div>
        </motion.div>

        {/* Main Title */}
        <motion.h1
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
          style={{
            fontSize: 'clamp(4rem, 8vw, 8rem)',
            fontWeight: 900,
            lineHeight: 0.8,
            letterSpacing: '-0.05em',
            color: 'white',
            marginBottom: '3rem'
          }}
        >
          <span style={{ display: 'block' }}>Control</span>
          <span style={{ display: 'block', color: '#9CA3AF' }}>Center</span>
        </motion.h1>

        {/* Subtitle */}
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
          style={{
            fontSize: '1.25rem',
            fontWeight: 500,
            color: '#D1D5DB',
            lineHeight: 1.6,
            maxWidth: '64rem',
            margin: '0 auto 3rem auto',
            textAlign: 'center'
          }}
        >
          Configure your SEO optimization preferences, API keys, and automation settings.
          Fine-tune every aspect of your SEO workflow.
        </motion.p>

        {/* Settings Categories Preview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 max-w-4xl mx-auto"
        >
          {[
            { 
              icon: "🔑", 
              title: "API Keys", 
              desc: "Google, OpenAI integrations",
              color: "from-blue-500/20 to-blue-600/20"
            },
            { 
              icon: "⚡", 
              title: "Automation", 
              desc: "Bulk processing settings",
              color: "from-yellow-500/20 to-yellow-600/20"
            },
            { 
              icon: "🎯", 
              title: "Preferences", 
              desc: "Default optimization rules",
              color: "from-green-500/20 to-green-600/20"
            },
            { 
              icon: "📊", 
              title: "Analytics", 
              desc: "Tracking and reporting",
              color: "from-purple-500/20 to-purple-600/20"
            }
          ].map((category, index) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
              className={`bg-gradient-to-br ${category.color} rounded-2xl p-6 border border-white/10 text-center hover:scale-105 transition-transform duration-300`}
            >
              <div className="text-3xl mb-3">{category.icon}</div>
              <div className="font-bold text-sm mb-2">{category.title}</div>
              <div className="text-xs text-gray-400 leading-relaxed">{category.desc}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Quick Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1, ease: "easeOut" }}
          className="flex justify-center items-center space-x-8 mt-12 text-sm text-gray-400"
        >
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            <span>System Active</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
            <span>APIs Connected</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" />
            <span>Auto-Sync Enabled</span>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
