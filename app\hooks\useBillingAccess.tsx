import { useEffect, useState } from "react";
import { useFetcher } from "@remix-run/react";
import { BillingPlan, SubscriptionData } from "../services/billing.server";

// Type for the serialized data that comes from the API
export interface BillingAccessApiData {
  hasAccess: boolean;
  plan?: BillingPlan;
  subscription?: any; // Use any for the serialized subscription data
  trialExpired?: boolean;
  isLoading: boolean;
  error?: string;
}

// Type for the internal state (same as before)
export interface BillingAccessData {
  hasAccess: boolean;
  plan?: BillingPlan;
  subscription?: SubscriptionData;
  trialExpired?: boolean;
  isLoading: boolean;
  error?: string;
}

export interface BillingAccessOptions {
  requireActiveSubscription?: boolean;
  allowPayPerUse?: boolean;
  blockOnTrialExpired?: boolean;
}

/**
 * Hook to check billing access and enforce payment requirements
 */
export function useBillingAccess(options: BillingAccessOptions = {}) {
  const {
    requireActiveSubscription = true,
    allowPayPerUse = true,
    blockOnTrialExpired = true
  } = options;

  const fetcher = useFetcher<BillingAccessApiData>();
  const [billingData, setBillingData] = useState<BillingAccessData>({
    hasAccess: false,
    isLoading: true
  });

  useEffect(() => {
    // Fetch billing status on mount
    if (fetcher.state === 'idle' && !fetcher.data) {
      fetcher.load('/app/api/billing-status');
    }
  }, [fetcher]);

  useEffect(() => {
    if (fetcher.data) {
      setBillingData({
        hasAccess: fetcher.data.hasAccess,
        plan: fetcher.data.plan,
        subscription: fetcher.data.subscription as SubscriptionData | undefined,
        trialExpired: fetcher.data.trialExpired,
        error: fetcher.data.error,
        isLoading: false
      });
    }
  }, [fetcher.data]);

  useEffect(() => {
    if (fetcher.state === 'loading') {
      setBillingData(prev => ({ ...prev, isLoading: true }));
    }
  }, [fetcher.state]);

  // Determine if user has access based on options
  const hasAccess = () => {
    if (billingData.isLoading) return false;
    
    // If trial expired and we block on trial expiration
    if (blockOnTrialExpired && billingData.trialExpired) {
      return false;
    }

    // If we require active subscription
    if (requireActiveSubscription) {
      return billingData.subscription?.status === 'ACTIVE' || 
             (billingData.subscription?.status === 'PENDING' && 
              billingData.subscription?.trialDays && 
              billingData.subscription.trialDays > 0);
    }

    // If we allow pay-per-use, user always has potential access
    if (allowPayPerUse) {
      return true;
    }

    return billingData.hasAccess;
  };

  // Check if user needs to upgrade/pay
  const needsUpgrade = () => {
    return !hasAccess() && !billingData.isLoading;
  };

  // Check if user is on free trial
  const isOnTrial = () => {
    return billingData.subscription?.status === 'PENDING' && 
           billingData.subscription?.trialDays && 
           billingData.subscription.trialDays > 0;
  };

  // Get trial days remaining
  const getTrialDaysRemaining = () => {
    if (!isOnTrial()) return 0;
    return billingData.subscription?.trialDays || 0;
  };

  // Refresh billing status
  const refreshBillingStatus = () => {
    fetcher.load('/app/api/billing-status');
  };

  return {
    ...billingData,
    hasAccess: hasAccess(),
    needsUpgrade: needsUpgrade(),
    isOnTrial: isOnTrial(),
    trialDaysRemaining: getTrialDaysRemaining(),
    refreshBillingStatus
  };
}

/**
 * Hook specifically for pay-per-use billing
 */
export function usePayPerUseBilling() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastPurchase, setLastPurchase] = useState<{
    id: string;
    amount: number;
    productCount: number;
    status: string;
  } | null>(null);

  const createPurchase = async (productCount: number, selectedProducts: string[]) => {
    setIsProcessing(true);
    
    try {
      const formData = new FormData();
      formData.append('action', 'create_pay_per_use_purchase');
      formData.append('productCount', productCount.toString());
      formData.append('selectedProducts', JSON.stringify(selectedProducts));
      
      const response = await fetch('/app/billing/pay-per-use', {
        method: 'POST',
        body: formData
      });
      
      if (response.ok) {
        // Handle redirect to Shopify billing
        window.location.href = response.url;
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create purchase');
      }
    } catch (error) {
      console.error('Pay-per-use purchase error:', error);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  };

  const checkPurchaseStatus = async (purchaseId: string) => {
    try {
      const formData = new FormData();
      formData.append('action', 'check_purchase_status');
      formData.append('purchaseId', purchaseId);
      
      const response = await fetch('/app/billing/pay-per-use', {
        method: 'POST',
        body: formData
      });
      
      if (response.ok) {
        const data = await response.json();
        setLastPurchase({
          id: purchaseId,
          amount: data.amount,
          productCount: data.productCount,
          status: data.status
        });
        return data;
      } else {
        throw new Error('Failed to check purchase status');
      }
    } catch (error) {
      console.error('Purchase status check error:', error);
      throw error;
    }
  };

  return {
    isProcessing,
    lastPurchase,
    createPurchase,
    checkPurchaseStatus
  };
}

/**
 * Component wrapper that enforces billing access
 */
export function withBillingAccess<T extends object>(
  Component: React.ComponentType<T>,
  options: BillingAccessOptions = {}
) {
  return function BillingProtectedComponent(props: T) {
    const billingAccess = useBillingAccess(options);
    
    if (billingAccess.isLoading) {
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Checking billing status...</p>
          </div>
        </div>
      );
    }
    
    if (billingAccess.needsUpgrade) {
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center max-w-md">
            <h2 className="text-2xl font-bold mb-4">Upgrade Required</h2>
            <p className="text-muted-foreground mb-6">
              {billingAccess.trialExpired 
                ? "Your free trial has ended. Please choose a plan to continue."
                : "Please choose a billing plan to access this feature."
              }
            </p>
            <div className="space-y-3">
              <a 
                href="/app/billing/pricing" 
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
              >
                Choose Plan
              </a>
              <a 
                href="/app/billing" 
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 ml-3"
              >
                View Billing
              </a>
            </div>
          </div>
        </div>
      );
    }
    
    return <Component {...props} />;
  };
}
