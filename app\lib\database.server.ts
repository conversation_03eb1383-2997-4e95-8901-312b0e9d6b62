/**
 * Production-Grade Database Layer
 * Handles both Prisma (development) and Turso (production) seamlessly
 */

import { PrismaClient } from "@prisma/client";
import { createClient, type Client } from "@libsql/client";

// Database interface for type safety
export interface DatabaseClient {
  session: any;
  billingSubscription: any;
  billingPurchase: any;
  billingUsage: any;
  billingEvent: any;
  cSRFToken: any;
  rateLimitEntry: any;
  cacheEntry: any;
  $connect(): Promise<void>;
  $disconnect(): Promise<void>;
}

class ProductionDatabaseClient {
  private prismaClient: PrismaClient | null = null;
  private tursoClient: Client | null = null;
  private isProduction: boolean;

  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';

    if (this.isProduction && process.env.TURSO_DATABASE_URL && process.env.TURSO_AUTH_TOKEN) {
      // Production: Use Turso with separate URL and token
      this.tursoClient = createClient({
        url: process.env.TURSO_DATABASE_URL,
        authToken: process.env.TURSO_AUTH_TOKEN,
      });
      console.log('🚀 Using Turso database for production');
    } else {
      // Development: Use Prisma with SQLite
      this.prismaClient = new PrismaClient({
        log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
        errorFormat: 'pretty',
      });
      console.log('🔧 Using Prisma with SQLite for development');
    }
  }

  // Unified query interface
  async query(sql: string, params: any[] = []): Promise<any> {
    if (this.tursoClient) {
      // Use Turso for production
      const result = await this.tursoClient.execute({
        sql,
        args: params,
      });
      return result.rows;
    } else if (this.prismaClient) {
      // Use Prisma raw queries for development
      return await this.prismaClient.$queryRawUnsafe(sql, ...params);
    }
    throw new Error('No database client available');
  }

  // Connection management
  async connect(): Promise<void> {
    if (this.prismaClient) {
      await this.prismaClient.$connect();
    }
    // Turso doesn't need explicit connection
  }

  async disconnect(): Promise<void> {
    if (this.prismaClient) {
      await this.prismaClient.$disconnect();
    }
    if (this.tursoClient) {
      this.tursoClient.close();
    }
  }

  // Get the appropriate client for complex operations
  getPrismaClient(): PrismaClient {
    if (!this.prismaClient) {
      throw new Error('Prisma client not available in production mode');
    }
    return this.prismaClient;
  }

  getTursoClient(): Client {
    if (!this.tursoClient) {
      throw new Error('Turso client not available in development mode');
    }
    return this.tursoClient;
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      if (this.tursoClient) {
        await this.tursoClient.execute('SELECT 1');
        return true;
      } else if (this.prismaClient) {
        await this.prismaClient.$queryRaw`SELECT 1`;
        return true;
      }
      return false;
    } catch (error) {
      console.error('Database health check failed:', error);
      return false;
    }
  }
}

// Singleton instance
let databaseInstance: ProductionDatabaseClient | null = null;

export function getDatabaseClient(): ProductionDatabaseClient {
  if (!databaseInstance) {
    databaseInstance = new ProductionDatabaseClient();
  }
  return databaseInstance;
}

// For backward compatibility with existing Prisma code
export function getPrismaClient(): PrismaClient {
  const dbClient = getDatabaseClient();
  return dbClient.getPrismaClient();
}

// Graceful shutdown
process.on('SIGINT', async () => {
  if (databaseInstance) {
    await databaseInstance.disconnect();
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  if (databaseInstance) {
    await databaseInstance.disconnect();
  }
  process.exit(0);
});
