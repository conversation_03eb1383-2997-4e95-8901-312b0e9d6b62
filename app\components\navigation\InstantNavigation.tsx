import * as React from "react";
import { useNavigate, useLocation } from "@remix-run/react";
import { motion, AnimatePresence } from "framer-motion";

interface InstantNavigationContextType {
  navigate: (path: string) => void;
  isNavigating: boolean;
  preloadRoute: (path: string) => void;
}

const InstantNavigationContext = React.createContext<InstantNavigationContextType | null>(null);

export function useInstantNavigation() {
  const context = React.useContext(InstantNavigationContext);
  if (!context) {
    throw new Error("useInstantNavigation must be used within InstantNavigationProvider");
  }
  return context;
}

interface InstantNavigationProviderProps {
  children: React.ReactNode;
}

export function InstantNavigationProvider({ children }: InstantNavigationProviderProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const [isNavigating, setIsNavigating] = React.useState(false);
  const [preloadedRoutes, setPreloadedRoutes] = React.useState<Set<string>>(new Set());

  // Preload route data
  const preloadRoute = React.useCallback((path: string) => {
    if (preloadedRoutes.has(path)) return;
    
    // Create a hidden link and trigger prefetch
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = path;
    document.head.appendChild(link);
    
    setPreloadedRoutes(prev => new Set([...prev, path]));
    
    // Clean up after 30 seconds
    setTimeout(() => {
      document.head.removeChild(link);
    }, 30000);
  }, [preloadedRoutes]);

  // Instant navigation with transition
  const instantNavigate = React.useCallback((path: string) => {
    if (location.pathname === path) return;

    setIsNavigating(true);

    try {
      // Use requestAnimationFrame for smooth transition
      requestAnimationFrame(() => {
        navigate(path);
        // Reset navigation state after a short delay
        setTimeout(() => setIsNavigating(false), 100);
      });
    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback to direct navigation
      navigate(path);
      setIsNavigating(false);
    }
  }, [navigate, location.pathname]);

  // Preload common routes on mount
  React.useEffect(() => {
    const commonRoutes = [
      '/app',
      '/app/seo-dashboard',
      '/app/settings'
    ];

    commonRoutes.forEach(route => {
      if (route !== location.pathname) {
        setTimeout(() => preloadRoute(route), 1000);
      }
    });
  }, [location.pathname, preloadRoute]);

  const value = React.useMemo(() => ({
    navigate: instantNavigate,
    isNavigating,
    preloadRoute
  }), [instantNavigate, isNavigating, preloadRoute]);

  return (
    <InstantNavigationContext.Provider value={value}>
      {/* Simplified navigation without AnimatePresence to avoid blank screen issues */}
      <motion.div
        key={location.pathname}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.2, ease: "easeInOut" }}
      >
        {children}
      </motion.div>

      {/* Navigation Loading Overlay */}
      <AnimatePresence>
        {isNavigating && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.1 }}
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 pointer-events-none"
          >
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </InstantNavigationContext.Provider>
  );
}

// Enhanced Button component with instant navigation and preloading
interface InstantNavButtonProps {
  to: string;
  children: React.ReactNode;
  className?: string;
  onMouseEnter?: () => void;
  onClick?: () => void;
  disabled?: boolean;
  size?: "sm" | "default" | "lg";
}

export function InstantNavButton({ 
  to, 
  children, 
  className = "", 
  onMouseEnter,
  onClick,
  disabled = false,
  size = "default"
}: InstantNavButtonProps) {
  const { navigate, preloadRoute } = useInstantNavigation();
  
  const handleMouseEnter = React.useCallback(() => {
    preloadRoute(to);
    onMouseEnter?.();
  }, [to, preloadRoute, onMouseEnter]);
  
  const handleClick = React.useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    if (disabled) return;
    
    onClick?.();
    navigate(to);
  }, [navigate, to, onClick, disabled]);
  
  const sizeClasses = {
    sm: "h-10 px-6 py-2 text-xs",
    default: "h-12 px-8 py-3 text-sm", 
    lg: "h-14 px-10 py-4 text-base"
  };
  
  return (
    <button
      onMouseEnter={handleMouseEnter}
      onClick={handleClick}
      disabled={disabled}
      className={`
        inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full font-bold 
        transition-all duration-300 disabled:pointer-events-none disabled:opacity-50 
        relative overflow-hidden group active:scale-95 transform hover:scale-[1.02]
        bg-white text-black hover:bg-gray-100 shadow-lg hover:shadow-2xl 
        border border-gray-200 hover:border-gray-300
        ${sizeClasses[size]}
        ${className}
      `}
    >
      {children}
    </button>
  );
}
