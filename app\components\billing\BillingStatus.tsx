import * as React from "react";
import { <PERSON> } from "@remix-run/react";
import { motion } from "framer-motion";
import { Button as UIButton } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  CreditCard, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Star,
  Zap
} from "lucide-react";
import { BillingPlan, SubscriptionData } from "../../services/billing.server";

interface BillingStatusProps {
  subscription?: SubscriptionData;
  plan?: BillingPlan;
  hasAccess: boolean;
  monthlyUsage?: {
    productsOptimized: number;
    totalSpent: number;
  };
}

export function BillingStatus({
  subscription,
  plan,
  hasAccess,
  monthlyUsage
}: BillingStatusProps) {
  const getStatusIcon = () => {
    if (!subscription) {
      return <CreditCard className="w-5 h-5 text-gray-500" />;
    }

    switch (subscription.status) {
      case 'ACTIVE':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'PENDING':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      default:
        return <CreditCard className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusBadge = () => {
    if (!subscription) {
      return <Badge variant="secondary">No Plan</Badge>;
    }

    switch (subscription.status) {
      case 'ACTIVE':
        return <Badge variant="default" className="bg-green-500">Active</Badge>;
      case 'PENDING':
        return <Badge variant="destructive">Payment Required</Badge>;
      case 'CANCELLED':
        return <Badge variant="secondary">Cancelled</Badge>;
      default:
        return <Badge variant="secondary">{subscription.status}</Badge>;
    }
  };



  // If no access, show upgrade prompt
  if (!hasAccess) {
    return (
      <div className="bg-white/10 border border-white/20 rounded-3xl p-8">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Star className="w-5 h-5 text-white" />
            <h3 className="text-lg font-bold text-white">Upgrade Required</h3>
          </div>
          {getStatusBadge()}
        </div>
        <p className="text-white/70 mb-6">
          Choose a plan to start optimizing your products with AI-powered SEO tools.
        </p>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className="p-3 bg-white/10 border border-white/20 rounded-2xl">
              <div className="text-lg font-bold text-white">$199.99/year</div>
              <div className="text-sm text-white/70">Annual Plan</div>
              <div className="text-xs text-green-400">Best Value</div>
            </div>
            <div className="p-3 bg-white/10 border border-white/20 rounded-2xl">
              <div className="text-lg font-bold text-white">$19.99/month</div>
              <div className="text-sm text-white/70">Monthly Plan</div>
            </div>
            <div className="p-3 bg-white/10 border border-white/20 rounded-2xl">
              <div className="text-lg font-bold text-white">$0.10</div>
              <div className="text-sm text-white/70">Per Product</div>
            </div>
          </div>

          <div className="flex gap-3">
            <UIButton asChild className="flex-1 bg-white text-black hover:bg-gray-100">
              <Link to="/app/billing/pricing">Choose Plan</Link>
            </UIButton>
            <UIButton asChild className="border-2 border-white/40 bg-transparent text-white hover:bg-white hover:text-black">
              <Link to="/app/billing">View Details</Link>
            </UIButton>
          </div>
        </div>
      </div>
    );
  }

  // Show current plan status
  return (
    <div className="bg-white/10 border border-white/20 rounded-3xl p-8">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          {getStatusIcon()}
          <h3 className="text-lg font-bold text-white">
            {plan?.name || 'Current Plan'}
          </h3>
        </div>
        {getStatusBadge()}
      </div>
      <p className="text-white/70 mb-6">
        {plan?.description || 'Your billing plan and status'}
      </p>
      <div className="space-y-4">
        {/* Plan Details */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-sm text-white/70">Plan Type</div>
            <div className="font-semibold text-white">
              {plan?.type === 'pay_per_use'
                ? 'Pay-Per-Use'
                : plan?.type === 'annual'
                  ? 'Annual Subscription'
                  : 'Monthly Subscription'
              }
            </div>
          </div>
          <div>
            <div className="text-sm text-white/70">Cost</div>
            <div className="font-semibold text-white">
              {plan?.type === 'pay_per_use'
                ? `$${plan.price} per product`
                : `$${plan?.price}/${plan?.type === 'annual' ? 'year' : 'month'}`
              }
            </div>
          </div>
        </div>



        {/* Usage This Month */}
        {monthlyUsage && (
          <div className="grid grid-cols-2 gap-4 pt-2 border-t border-white/20">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">
                {monthlyUsage.productsOptimized}
              </div>
              <div className="text-sm text-white/70">Products Optimized</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                ${monthlyUsage.totalSpent.toFixed(2)}
              </div>
              <div className="text-sm text-white/70">
                {plan?.type === 'pay_per_use' ? 'Total Spent' : 'Plan Value'}
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <UIButton asChild className="flex-1 h-10 px-6 py-2 text-xs border-2 border-white/40 bg-transparent text-white hover:bg-white hover:text-black">
            <Link to="/app/billing">Manage Billing</Link>
          </UIButton>
          {plan?.type !== 'annual' && (
            <UIButton asChild className="h-10 px-6 py-2 text-xs bg-white text-black hover:bg-gray-100">
              <Link to="/app/billing/pricing">Upgrade</Link>
            </UIButton>
          )}
        </div>
      </div>
    </div>
  );
}

// Compact version for header/sidebar
export function BillingStatusCompact({
  subscription,
  plan,
  hasAccess
}: Pick<BillingStatusProps, 'subscription' | 'plan' | 'hasAccess'>) {
  if (!hasAccess) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 bg-red-500/20 border border-red-500/40 rounded-2xl">
        <AlertTriangle className="w-4 h-4 text-red-400" />
        <span className="text-sm font-medium text-red-300">
          No Active Plan
        </span>
        <UIButton asChild className="ml-auto h-10 px-6 py-2 text-xs bg-white text-black hover:bg-gray-100">
          <Link to="/app/billing/pricing">Upgrade</Link>
        </UIButton>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2 px-3 py-2 bg-green-500/20 border border-green-500/40 rounded-2xl">
      <CheckCircle className="w-4 h-4 text-green-400" />
      <div className="flex-1">
        <div className="text-sm font-medium text-green-300">
          {plan?.name || 'Active Plan'}
        </div>
      </div>
      <UIButton asChild className="h-10 px-6 py-2 text-xs bg-white text-black hover:bg-gray-100 rounded-2xl">
        <Link to="/app/billing">Manage</Link>
      </UIButton>
    </div>
  );
}
