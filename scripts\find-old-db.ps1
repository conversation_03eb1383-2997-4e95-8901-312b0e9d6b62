# Simple Database Pattern Detection Script
Write-Host "🔍 SCANNING FOR OLD DATABASE PATTERNS..." -ForegroundColor Cyan
Write-Host ""

$patterns = @(
    "import db from",
    "from `"../db.server`"",
    "from '../db.server'",
    "require(`"../db.server`")",
    "require('../db.server')",
    "new PrismaClient"
)

$totalIssues = 0
$foundFiles = @()

foreach ($pattern in $patterns) {
    Write-Host "🔍 Searching for: $pattern" -ForegroundColor Yellow
    
    $files = Get-ChildItem -Path "app" -Recurse -Include "*.ts","*.tsx","*.js","*.jsx" | 
        Select-String -Pattern $pattern -SimpleMatch | 
        Select-Object Filename, LineNumber, Line
    
    if ($files) {
        Write-Host "   ❌ Found $($files.Count) matches:" -ForegroundColor Red
        foreach ($file in $files) {
            $relativePath = $file.Filename -replace [regex]::Escape($PWD.Path), ""
            Write-Host "      $relativePath : Line $($file.LineNumber)" -ForegroundColor Red
            Write-Host "         $($file.Line.Trim())" -ForegroundColor Gray
            $foundFiles += $relativePath
            $totalIssues++
        }
    } else {
        Write-Host "   ✅ No matches found" -ForegroundColor Green
    }
    Write-Host ""
}

Write-Host "📋 SCAN RESULTS" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan

if ($totalIssues -eq 0) {
    Write-Host "🎉 PERFECT! NO OLD DATABASE PATTERNS FOUND!" -ForegroundColor Green
    Write-Host "✅ Your codebase is 100% migrated to Turso!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "❌ FOUND $totalIssues ISSUES IN $($foundFiles | Sort-Object -Unique | Measure-Object).Count FILES!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Files that need fixing:" -ForegroundColor Yellow
    $foundFiles | Sort-Object -Unique | ForEach-Object {
        Write-Host "   • $_" -ForegroundColor Red
    }
    Write-Host ""
    Write-Host "🔧 ACTION REQUIRED:" -ForegroundColor Yellow
    Write-Host "Replace all old database imports with Turso adapter!" -ForegroundColor White
    exit 1
}
